import React from 'react';
import { View, Text, TouchableOpacity, Switch, ScrollView } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useNavigation } from 'expo-router';
import { Ionicons, MaterialIcons, MaterialCommunityIcons } from '@expo/vector-icons';
import { useColorScheme } from '~/lib/useColorScheme';

export default function SettingsScreen() {
  const navigation = useNavigation();
  const { colors, colorScheme, setColorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const toggleDarkMode = () => {
    setColorScheme(isDark ? 'light' : 'dark');
  };

  return (
    <View className={`flex-1 pt-12`} style={{ backgroundColor: colors.background }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />

      <View className="flex-row items-center justify-center px-4 pb-4">
        <Text className={`font-medium text-xl ${isDark ? 'text-white' : 'text-black'}`}>
          Settings
        </Text>
      </View>

      <ScrollView className="mt-10 flex-1">
        <View className="mb-6">
          <Text
            className={`mb-2 px-4 font-medium text-sm uppercase ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            Appearance
          </Text>

          <View
            className={`flex-row items-center justify-between border-b px-4 py-[14px] `}
            style={{ borderBottomColor: colors.grey5 }}>
            <View className="flex-1 flex-row items-center">
              <Ionicons
                name={isDark ? 'moon' : 'sunny'}
                size={22}
                color={isDark ? '#fff' : '#000'}
                className="mr-3"
              />
              <Text className={`text-base ${isDark ? 'text-white' : 'text-black'}`}>Dark Mode</Text>
            </View>
            <Switch
              value={isDark}
              onValueChange={toggleDarkMode}
              trackColor={{ false: '#767577', true: '#5A4FCF' }}
              thumbColor={'#fff'}
            />
          </View>
        </View>

        <View className="mb-6">
          <Text
            className={`mb-2 px-4 font-medium text-sm uppercase ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            Notifications
          </Text>

          <SettingItem
            icon="notifications-outline"
            text="Push Notifications"
            hasSwitch={true}
            isDark={isDark}
            colors={colors}
          />

          <SettingItem
            icon="mail-outline"
            text="Email Notifications"
            hasSwitch={true}
            isDark={isDark}
            colors={colors}
          />

          <SettingItem
            icon="calendar-outline"
            text="Event Reminders"
            hasSwitch={true}
            isDark={isDark}
            colors={colors}
          />
        </View>

        <View className="mb-6">
          <Text
            className={`mb-2 px-4 font-medium text-sm uppercase ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            Privacy
          </Text>

          <SettingItem
            icon="lock-closed-outline"
            text="Account Privacy"
            isDark={isDark}
            colors={colors}
          />

          <SettingItem
            icon="eye-off-outline"
            text="Blocked Users"
            isDark={isDark}
            hasChevron={true}
            colors={colors}
          />

          <SettingItem
            icon="location-outline"
            text="Location Services"
            hasSwitch={true}
            isDark={isDark}
            colors={colors}
          />
        </View>

        <View className="mb-6">
          <Text
            className={`mb-2 px-4 font-medium text-sm uppercase ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            Support
          </Text>

          <SettingItem
            icon="help-circle-outline"
            text="Help Center"
            isDark={isDark}
            hasChevron={true}
            colors={colors}
          />

          <SettingItem
            icon="document-text-outline"
            text="Terms and Conditions"
            isDark={isDark}
            hasChevron={true}
            colors={colors}
          />

          <SettingItem
            icon="shield-checkmark-outline"
            text="Privacy Policy"
            isDark={isDark}
            hasChevron={true}
            colors={colors}
          />
        </View>

        <View className="mb-6">
          <SettingItem
            icon="log-out-outline"
            text="Logout"
            isDark={isDark}
            isDestructive={true}
            colors={colors}
          />
        </View>

        <Text className={`py-5 text-center text-sm ${isDark ? 'text-gray-600' : 'text-gray-400'}`}>
          Version 1.0.0
        </Text>
      </ScrollView>
    </View>
  );
}

// Setting Item Component
function SettingItem({
  icon,
  text,
  hasSwitch = false,
  hasChevron = false,
  isDark,
  isDestructive = false,
  colors,
}) {
  return (
    <View
      className={`flex-row items-center justify-between border-b px-4 py-[14px]`}
      style={{ borderBottomColor: colors.grey5 }}>
      <View className="flex-1 flex-row items-center">
        <Ionicons
          name={icon}
          size={22}
          color={isDestructive ? (isDark ? '#ff6b6b' : '#e53935') : isDark ? '#fff' : '#000'}
          className="mr-3"
        />
        <Text
          className={`text-base ${
            isDestructive
              ? isDark
                ? 'text-red-400'
                : 'text-red-600'
              : isDark
                ? 'text-white'
                : 'text-black'
          }`}>
          {text}
        </Text>
      </View>

      {hasSwitch && (
        <Switch
          trackColor={{ false: '#767577', true: '#5A4FCF' }}
          thumbColor={'#fff'}
          value={false}
        />
      )}

      {hasChevron && (
        <MaterialIcons name="chevron-right" size={22} color={isDark ? '#aaa' : '#777'} />
      )}

      {!hasSwitch && !hasChevron && !isDestructive && <View className="w-10" />}
    </View>
  );
}
