import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  FlatList,
  TextInput,
  ScrollView,
  Platform,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useNavigation, useRouter } from 'expo-router';
import { Ionicons, MaterialIcons, FontAwesome } from '@expo/vector-icons';
import { useColorScheme } from '~/lib/useColorScheme';
import { Friend } from '~/types/chat_type';

// Mock data for friends
const mockFriends: (Friend & { hasStory?: boolean })[] = [
  {
    id: 2,
    name: '<PERSON>',
    avatar: 'https://images.unsplash.com/photo-1599566150163-29194dcaad36',
    status: 'online',
    hasStory: true,
  },
  {
    id: 3,
    name: '<PERSON>',
    avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330',
    status: 'online',
    hasStory: true,
  },
  {
    id: 4,
    name: '<PERSON>',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d',
    status: 'offline',
    hasStory: false,
  },
  {
    id: 5,
    name: '<PERSON>',
    avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2',
    status: 'online',
    hasStory: false,
  },
  {
    id: 6,
    name: 'David Brown',
    avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d',
    status: 'offline',
    hasStory: false,
  },
  {
    id: 7,
    name: 'Emily Davis',
    avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb',
    status: 'online',
    hasStory: true,
  },
  {
    id: 8,
    name: 'Alex Wilson',
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e',
    status: 'offline',
    hasStory: false,
  },
];

export default function FriendsScreen() {
  const navigation = useNavigation();
  const router = useRouter();
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [searchQuery, setSearchQuery] = useState('');
  const [filteredFriends, setFilteredFriends] = useState(mockFriends);

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    if (text.trim() === '') {
      setFilteredFriends(mockFriends);
    } else {
      const filtered = mockFriends.filter((friend) =>
        friend.name.toLowerCase().includes(text.toLowerCase())
      );
      setFilteredFriends(filtered);
    }
  };

  const handleViewProfile = (friend: Friend) => {
    // Navigate to profile view
    router.push({
      pathname: '/profile',
      params: { userId: friend.id },
    });
  };

  const handleChat = (friend: Friend) => {
    // Navigate to chat
    router.push({
      pathname: '/chat',
      params: { userId: friend.id, name: friend.name },
    });
  };

  const handleViewStory = (friend: Friend) => {
    // Navigate to story view
    router.push({
      pathname: '/story/view',
      params: { userId: friend.id },
    });
  };

  const renderFriendItem = ({ item }: { item: Friend & { hasStory?: boolean } }) => {
    return (
      <View
        className="flex-row items-center border-b px-4 py-4"
        style={{ borderBottomColor: colors.grey5 }}>
        <TouchableOpacity
          className="relative"
          onPress={() => item.hasStory && handleViewStory(item)}>
          <View
            className={`${item.hasStory ? 'border-2 border-violet-600 p-0.5' : ''} rounded-full`}>
            <Image source={{ uri: item.avatar }} className="h-14 w-14 rounded-full" />
          </View>
          <View
            className={`absolute bottom-0 right-0 h-3.5 w-3.5 rounded-full border border-white ${item.status === 'online' ? 'bg-green-500' : 'bg-gray-400'}`}
          />
        </TouchableOpacity>
        <View className="ml-3 flex-1">
          <Text
            className={`font-medium text-base ${isDark ? 'text-white' : 'text-black'}`}
            numberOfLines={1}>
            {item.name}
          </Text>
          <Text className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            {item.status === 'online' ? 'Online' : 'Offline'}
          </Text>
        </View>
        <View className="flex-row">
          <TouchableOpacity
            className="mr-3 h-10 w-10 items-center justify-center rounded-full"
            style={{ backgroundColor: colors.grey5 }}
            onPress={() => handleChat(item)}>
            <Ionicons
              name="chatbubble-outline"
              size={20}
              color={isDark ? colors.foreground : colors.foreground}
            />
          </TouchableOpacity>
          <TouchableOpacity
            className="h-10 w-10 items-center justify-center rounded-full"
            style={{ backgroundColor: colors.grey5 }}
            onPress={() => handleViewProfile(item)}>
            <Ionicons
              name="person-outline"
              size={20}
              color={isDark ? colors.foreground : colors.foreground}
            />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <View className="flex-1" style={{ backgroundColor: colors.background }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />

      <View className="flex-row items-center justify-between px-4 pb-4 pt-12">
        <TouchableOpacity className="p-2" onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={isDark ? '#fff' : '#000'} />
        </TouchableOpacity>
        <Text className={`font-medium text-lg ${isDark ? 'text-white' : 'text-black'}`}>
          Friends
        </Text>
        <View className="w-10" />
      </View>

      <View className="mb-4 px-4">
        <View
          className="flex-row items-center rounded-xl px-3 py-2"
          style={{ backgroundColor: colors.grey5 }}>
          <Ionicons name="search-outline" size={20} color={isDark ? colors.grey2 : colors.grey2} />
          <TextInput
            className={`ml-2 flex-1 ${isDark ? 'text-white' : 'text-black'}`}
            placeholder="Search friends"
            placeholderTextColor={isDark ? colors.grey2 : colors.grey2}
            value={searchQuery}
            onChangeText={handleSearch}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => handleSearch('')}>
              <Ionicons
                name="close-circle"
                size={20}
                color={isDark ? colors.grey2 : colors.grey2}
              />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {filteredFriends.length > 0 ? (
        <FlatList
          data={filteredFriends}
          renderItem={renderFriendItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={{ paddingBottom: Platform.OS === 'android' ? 100 : 40 }}
          showsVerticalScrollIndicator={false}
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          windowSize={10}
          scrollEventThrottle={16}
          overScrollMode="never"
          bounces={false}
        />
      ) : (
        <View className="flex-1 items-center justify-center px-4">
          <Ionicons name="people-outline" size={60} color={isDark ? colors.grey3 : colors.grey4} />
          <Text
            className={`mt-4 text-center font-medium text-lg ${isDark ? 'text-white' : 'text-black'}`}>
            No Friends Found
          </Text>
          <Text className={`mt-2 text-center ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            {searchQuery ? `No results for "${searchQuery}"` : "You don't have any friends yet"}
          </Text>
        </View>
      )}
    </View>
  );
}
