import React, { useRef, useState, useCallback } from 'react';
import { Drawer } from 'expo-router/drawer';
import { View, TouchableOpacity, Image, Text, Pressable } from 'react-native';
import {
  Ionicons,
  MaterialIcons,
  FontAwesome5,
  MaterialCommunityIcons,
  Entypo,
} from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import BottomSheet, { BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import { Input, InputField } from '~/components/ui/input';
import { Button, ButtonText } from '@/components/ui/button';
import { useColorScheme } from '~/lib/useColorScheme';
import { RenderBackdrop } from '~/components/RenderBackdrop';
import { AuthService } from '~/services/AuthService';
import { Toast } from 'toastify-react-native';

// Mock user data - would come from auth/context in a real app
const userData = {
  name: '<PERSON><PERSON><PERSON>',
  profileImage: 'https://images.unsplash.com/photo-1507152832244-10d45c7eda57',
  upFor: '',
  friends: 243,
  events: 15,
  attendedEvents: 27,
};

const DrawerLayout = () => {
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const router = useRouter();

  const logout = () => {
    AuthService.logout();
  };

  // Custom drawer content
  const DrawerContent = (props) => {
    return (
      <View className="flex-1 pt-12" style={{ backgroundColor: colors.background }}>
        {/* Profile Section */}
        <TouchableOpacity
          onPress={() => {
            router.push('/profile');
          }}
          className="mb-2 flex-row items-center px-6 py-5">
          <Image
            source={{ uri: userData.profileImage }}
            className="mr-4 h-[50px] w-[50px] rounded-full border-2 border-light-primary dark:border-dark-primary"
          />
          <View className="flex-1">
            <Text className="mb-1 font-medium text-lg" style={{ color: colors.foreground }}>
              {userData.name}
            </Text>
            {/*   <View className="flex-row items-center">
              {currentUpFor ? (
                <Text className="text-sm italic text-light-primary dark:text-dark-primary">
                  Up for: {currentUpFor}
                </Text>
              ) : (
                <Text className="text-sm text-dark-background dark:text-light-border">
                  Tap to edit profile
                </Text>
              )}
            </View> */}
          </View>
        </TouchableOpacity>

        {/* Divider */}
        <View
          className="mx-auto h-[1px] w-full border-hairline"
          style={{ borderColor: colors.grey5 }}
        />

        {/* Drawer Items */}
        <View className="flex-1 px-2 pt-6">
          {/*  <DrawerItem
            icon={<Ionicons name="add-circle-outline" size={24} color={colors.foreground} />}
            label="Add a story"
            onPress={() => router.push('/story/create')}
            isDark={isDark}
          />

          <DrawerItem
            icon={<Ionicons name="images-outline" size={24} color={colors.foreground} />}
            label="My Stories"
            onPress={() => router.push('/story/my-stories')}
            isDark={isDark}
          /> */}
          <DrawerItem
            icon={<Ionicons name="people-outline" size={24} color={colors.foreground} />}
            label="Friends"
            onPress={() => router.push('/friends')}
            isDark={isDark}
          />
          <DrawerItem
            icon={<FontAwesome5 name="ticket-alt" size={20} color={colors.foreground} />}
            label="My tickets"
            onPress={() => router.push('/tickets')}
            isDark={isDark}
          />
          {/*      <DrawerItem
            icon={<Ionicons name="person-outline" size={24} color={colors.foreground} />}
            label="My Account"
            onPress={() => router.push('/profile')}
            isDark={isDark}
          /> */}
          <DrawerItem
            icon={<Ionicons name="settings-outline" size={24} color={colors.foreground} />}
            label="Settings"
            onPress={() => router.push('/settings')}
            isDark={isDark}
          />
          <DrawerItem
            icon={<MaterialIcons name="help-outline" size={24} color={colors.foreground} />}
            label="Help center"
            onPress={() => router.push('/help')}
            isDark={isDark}
          />
          <DrawerItem
            icon={<MaterialIcons name="description" size={24} color={colors.foreground} />}
            label="Terms and conditions"
            onPress={() => router.push('/Auth/termsAndConditions')}
            isDark={isDark}
          />
          <DrawerItem
            icon={<MaterialIcons name="notifications" size={24} color={colors.foreground} />}
            label="Notifications"
            onPress={() => router.push('/notifications')}
            isDark={isDark}
          />
          <View
            className="mx-auto my-6 h-[1px] w-full border-hairline"
            style={{ borderColor: colors.grey5 }}
          />
          <DrawerItem
            icon={<MaterialIcons name="logout" size={24} color={isDark ? '#ff6b6b' : '#e53935'} />}
            label="Logout"
            onPress={() => {
              logout();
            }}
            isDark={isDark}
            isDestructive
          />
        </View>
      </View>
    );
  };

  return (
    <>
      <Drawer
        screenOptions={{
          headerShown: false,
          swipeEdgeWidth: 0,
          drawerPosition: 'right',
          drawerStyle: {
            backgroundColor: colors.background,
            width: '80%',
            borderTopRightRadius: 20,
            borderBottomRightRadius: 20,
            shadowColor: isDark ? '#000' : '#333',
            shadowOffset: { width: 1, height: 1 },
            shadowOpacity: isDark ? 0.4 : 0.2,
            shadowRadius: 4,
            elevation: 5,
          },
          overlayColor: 'transparent',
          headerTransparent: true,
          headerStyle: {
            backgroundColor: 'transparent',
            elevation: 0,
            shadowOpacity: 0,
          },
          headerLeft: () => null,
        }}
        drawerContent={(props) => <DrawerContent {...props} />}>
        <Drawer.Screen
          name="(tabs)"
          options={{
            headerTitle: '',
            drawerLabel: 'Tabs',
            drawerIcon: ({ size, color }) => (
              <MaterialIcons name="border-bottom" size={size} color={color} />
            ),
          }}
        />
      </Drawer>

      {/* Profile Bottom Sheet */}
      {/*   <BottomSheet
        ref={profileBottomSheetRef}
        index={-1}
        snapPoints={profileSnapPoints}
        enablePanDownToClose
        backdropComponent={RenderBackdrop}
        handleIndicatorStyle={{
          backgroundColor: isDark ? '#6b7280' : '#a1a1aa',
          width: 40,
        }}
        backgroundStyle={{
          backgroundColor: colors.background,
        }}>
        <BottomSheetView className="flex-1 p-4">
          <Text className={`mb-2 font-bold text-xl ${isDark ? 'text-white' : 'text-black'}`}>
            Edit Profile
          </Text>

          <View className="items-center my-4">
            <TouchableOpacity className="relative">
              <Image
                source={{ uri: userData.profileImage }}
                className="h-[120px] w-[120px] rounded-full"
              />
              <View className="absolute bottom-0 right-0 h-9 w-9 items-center justify-center rounded-full border-[3px] border-white bg-violet-600">
                <MaterialIcons name="edit" size={18} color="#fff" />
              </View>
            </TouchableOpacity>

            <Text
              className={`mb-2 mt-6 self-start text-base ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
              Display Name
            </Text>
            <Input
              variant="outline"
              size="md"
              className={`mb-4 w-full rounded-xl border-0 `}
              defaultValue={userData.name}>
              <InputField
                className={isDark ? 'font-medium text-white' : 'font-medium'}
                placeholderTextColor={isDark ? '#9ca3af' : undefined}
              />
            </Input>

            <Text
              className={`mb-2 self-start text-base ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
              Status
            </Text>
            <Input
              variant="outline"
              size="md"
              className={`mb-4 w-full rounded-xl border-0 `}
              defaultValue={currentUpFor}>
              <InputField
                className={isDark ? 'font-medium text-white' : 'font-medium'}
                placeholderTextColor={isDark ? '#9ca3af' : undefined}
                maxLength={20}
              />
            </Input>

            <View className="flex-row justify-between w-full my-5">
              <View className="items-center flex-1">
                <Text className={`font-bold text-2xl ${isDark ? 'text-white' : 'text-black'}`}>
                  {userData.friends}
                </Text>
                <Text className={`mt-1 text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                  Friends
                </Text>
              </View>
              <View className="items-center flex-1">
                <Text className={`font-bold text-2xl ${isDark ? 'text-white' : 'text-black'}`}>
                  {userData.events}
                </Text>
                <Text className={`mt-1 text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                  Events Created
                </Text>
              </View>
              <View className="items-center flex-1">
                <Text className={`font-bold text-2xl ${isDark ? 'text-white' : 'text-black'}`}>
                  {userData.attendedEvents}
                </Text>
                <Text className={`mt-1 text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                  Events Attended
                </Text>
              </View>
            </View>

            <Button
              size="md"
              variant="solid"
              className={`mt-4 w-full ${isDark ? 'bg-violet-700' : 'bg-violet-600'}`}
              onPress={closeProfileSheet}>
              <Text className="text-white">Save Changes</Text>
            </Button>
          </View>
        </BottomSheetView>
      </BottomSheet> */}
    </>
  );
};

// Drawer Item Component
const DrawerItem = ({ icon, label, onPress, isDark, isDestructive = false }) => {
  const { colors } = useColorScheme();

  // Determine text color class based on theme and destructive state
  const textColorClass = isDestructive
    ? 'text-light-error dark:text-dark-error'
    : 'text-light-text dark:text-dark-text';

  // Clone the icon element with the appropriate color
  const iconWithColor = React.cloneElement(icon, {
    color: isDestructive ? (isDark ? '#ff6b6b' : '#e53935') : colors.foreground,
  });

  return (
    <Pressable
      onPress={onPress}
      className="mb-2 flex-row items-center rounded-xl px-4 py-3.5 active:bg-gray-100 active:opacity-70 dark:active:bg-gray-100">
      <View className="mr-4 w-7 items-center justify-center">{iconWithColor}</View>
      <Text className={`flex-1 font-medium text-base`} style={{ color: colors.foreground }}>
        {label}
      </Text>
      {!isDestructive && (
        <MaterialIcons name="chevron-right" size={22} color={isDark ? '#aaa' : '#777'} />
      )}
    </Pressable>
  );
};

export default DrawerLayout;
