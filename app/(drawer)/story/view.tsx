import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Dimensions,
  Animated,
  StyleSheet,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useNavigation, useLocalSearchParams, useRouter } from 'expo-router';
import { Ionicons, MaterialIcons, Feather } from '@expo/vector-icons';
import { useColorScheme } from '~/lib/useColorScheme';
import { Story } from '~/types/story_type';
import { Video, ResizeMode, AVPlaybackStatus } from 'expo-av';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { FileService } from '~/services/FileService';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Mock data for stories
const mockStories: Story[] = [
  {
    id: 1,
    userId: 2,
    userName: '<PERSON>',
    userProfilePhoto: 'https://images.unsplash.com/photo-1599566150163-29194dcaad36',
    mediaUrl: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f',
    mediaType: 'image',
    caption: 'Team meeting today!',
    createdAt: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
    expiresAt: new Date(Date.now() + 82800000).toISOString(), // 23 hours from now
    viewers: [],
    textColor: '#FFFFFF',
  },
  {
    id: 2,
    userId: 2,
    userName: 'John Doe',
    userProfilePhoto: 'https://images.unsplash.com/photo-1599566150163-29194dcaad36',
    mediaUrl: 'https://images.unsplash.com/photo-1501281668745-f7f57925c3b4',
    mediaType: 'image',
    caption: 'Beautiful sunset',
    createdAt: new Date(Date.now() - 1800000).toISOString(), // 30 minutes ago
    expiresAt: new Date(Date.now() + 84600000).toISOString(), // 23.5 hours from now
    viewers: [],
    textColor: '#FFFFFF',
  },
  {
    id: 3,
    userId: 3,
    userName: 'Jane Smith',
    userProfilePhoto: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330',
    mediaUrl: 'https://images.unsplash.com/photo-1534438327276-14e5300c3a48',
    mediaType: 'image',
    caption: 'Morning coffee ☕',
    createdAt: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
    expiresAt: new Date(Date.now() + 79200000).toISOString(), // 22 hours from now
    viewers: [],
    textColor: '#FFFFFF',
  },
];

export default function ViewStoryScreen() {
  const navigation = useNavigation();
  const router = useRouter();
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const insets = useSafeAreaInsets();
  const params = useLocalSearchParams();
  const storyId = params.storyId ? Number(params.storyId) : null;
  const userId = params.userId ? Number(params.userId) : null;

  // Find the initial story and user stories
  const initialStory = storyId
    ? mockStories.find((s) => s.id === storyId)
    : userId
      ? mockStories.find((s) => s.userId === userId)
      : mockStories[0];

  const userStories = userId
    ? mockStories.filter((s) => s.userId === userId)
    : initialStory
      ? mockStories.filter((s) => s.userId === initialStory.userId)
      : [];

  const [currentStoryIndex, setCurrentStoryIndex] = useState(
    initialStory ? userStories.findIndex((s) => s.id === initialStory.id) : 0
  );
  const [currentUserIndex, setCurrentUserIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [paused, setPaused] = useState(false);
  const [videoLoaded, setVideoLoaded] = useState(false);
  const videoRef = useRef<Video>(null);

  // Animation for progress bar
  const progressAnim = useRef(new Animated.Value(0)).current;
  const progressAnimation = useRef<Animated.CompositeAnimation | null>(null);

  // Duration for each story
  const STORY_DURATION = 5000; // 5 seconds

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return `${diffInSeconds}s ago`;
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}m ago`;
    } else if (diffInSeconds < 86400) {
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    } else {
      return `${Math.floor(diffInSeconds / 86400)}d ago`;
    }
  };

  const startProgressAnimation = () => {
    progressAnim.setValue(0);
    progressAnimation.current = Animated.timing(progressAnim, {
      toValue: 1,
      duration: STORY_DURATION,
      useNativeDriver: false,
    });
    progressAnimation.current.start(({ finished }) => {
      if (finished) {
        goToNextStory();
      }
    });
  };

  const pauseProgressAnimation = () => {
    progressAnimation.current?.stop();
  };

  const resumeProgressAnimation = () => {
    startProgressAnimation();
  };

  const goToPreviousStory = () => {
    if (currentStoryIndex > 0) {
      setCurrentStoryIndex(currentStoryIndex - 1);
    } else {
      // Go to previous user's last story
      if (currentUserIndex > 0) {
        setCurrentUserIndex(currentUserIndex - 1);
        // TODO: Set the last story of the previous user
        setCurrentStoryIndex(0);
      } else {
        // We're at the first story of the first user, restart
        startProgressAnimation();
      }
    }
  };

  const goToNextStory = () => {
    if (currentStoryIndex < userStories.length - 1) {
      setCurrentStoryIndex(currentStoryIndex + 1);
    } else {
      // Go to next user's first story
      if (currentUserIndex < mockStories.length - 1) {
        setCurrentUserIndex(currentUserIndex + 1);
        setCurrentStoryIndex(0);
      } else {
        // We've reached the end, close the story viewer
        router.back();
      }
    }
  };

  const handlePress = (event: { nativeEvent: { locationX: number } }) => {
    const { locationX } = event.nativeEvent;
    const screenWidth = Dimensions.get('window').width;

    if (locationX < screenWidth / 3) {
      goToPreviousStory();
    } else if (locationX > (screenWidth * 2) / 3) {
      goToNextStory();
    } else {
      // Toggle pause/play
      if (paused) {
        setPaused(false);
        resumeProgressAnimation();
      } else {
        setPaused(true);
        pauseProgressAnimation();
      }
    }
  };

  useEffect(() => {
    // Reset loading state and start progress animation when story changes
    setLoading(true);
    if (progressAnimation.current) {
      progressAnimation.current.stop();
    }
  }, [currentStoryIndex, currentUserIndex]);

  const handleClose = () => {
    router.back();
  };

  const handleImageLoad = () => {
    setLoading(false);
    startProgressAnimation();
  };

  const handleVideoLoad = () => {
    setVideoLoaded(true);
    setLoading(false);
    startProgressAnimation();
  };

  // Reset video loaded state when story changes
  useEffect(() => {
    setVideoLoaded(false);
  }, [currentStoryIndex]);

  const currentStory = userStories[currentStoryIndex];

  if (!currentStory) {
    return (
      <View
        className="flex-1 items-center justify-center"
        style={{ backgroundColor: colors.background }}>
        <Text className={isDark ? 'text-white' : 'text-black'}>Story not found</Text>
        <TouchableOpacity
          className="mt-4 rounded-full bg-violet-600 px-6 py-3"
          onPress={handleClose}>
          <Text className="font-medium text-white">Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-black">
      <StatusBar style="light" />

      {/* Progress bars */}
      <View
        className="absolute left-0 right-0 z-10 flex-row px-2 pb-2"
        style={{ paddingTop: insets.top + 10 }}>
        {userStories.map((_, index) => (
          <View key={index} className="mx-0.5 h-1 flex-1 overflow-hidden rounded-full bg-gray-600">
            {index < currentStoryIndex ? (
              <View className="h-full bg-white" />
            ) : index === currentStoryIndex ? (
              <Animated.View
                className="h-full bg-white"
                style={{
                  width: progressAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%'],
                  }),
                }}
              />
            ) : null}
          </View>
        ))}
      </View>

      {/* User info */}
      <View
        className="absolute left-0 right-0 z-10 flex-row items-center justify-between px-4 pb-2"
        style={{ paddingTop: insets.top + 20 }}>
        <View className="flex-row items-center">
          <TouchableOpacity onPress={handleClose} className="mr-3">
            <Ionicons name="chevron-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Image
            source={{ uri: currentStory.userProfilePhoto }}
            className="mr-2 h-8 w-8 rounded-full"
          />
          <View>
            <Text className="font-medium text-white">{currentStory.userName}</Text>
            <Text className="text-xs text-gray-300">{formatTimeAgo(currentStory.createdAt)}</Text>
          </View>
        </View>
      </View>

      {/* Story content */}
      <TouchableOpacity activeOpacity={1} onPress={handlePress} className="flex-1 justify-center">
        {loading && (
          <ActivityIndicator
            size="large"
            color="#fff"
            style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              marginLeft: -20,
              marginTop: -20,
            }}
          />
        )}

        {currentStory.mediaType === 'image' ? (
          <Image
            source={{ uri: currentStory.mediaUrl }}
            className="h-full w-full"
            resizeMode="cover"
            onLoad={handleImageLoad}
          />
        ) : (
          <Video
            ref={videoRef}
            source={{ uri: currentStory.mediaUrl }}
            rate={1.0}
            volume={1.0}
            isMuted={false}
            resizeMode={ResizeMode.COVER}
            shouldPlay={!paused && videoLoaded}
            isLooping={false}
            style={{ width: '100%', height: '100%' }}
            onLoad={handleVideoLoad}
            onPlaybackStatusUpdate={(status: AVPlaybackStatus) => {
              if ('didJustFinish' in status && status.didJustFinish) {
                goToNextStory();
              }
            }}
            useNativeControls={false}
            progressUpdateIntervalMillis={100}
          />
        )}

        {/* Caption */}
        {currentStory.caption && (
          <View className="absolute bottom-[100px] left-0 right-0 px-4">
            <Text
              className="font-medium text-lg"
              style={{
                color: currentStory.textColor,
                textShadowColor: 'rgba(0,0,0,0.8)',
                textShadowOffset: { width: 1, height: 1 },
                textShadowRadius: 3,
              }}>
              {currentStory.caption}
            </Text>
          </View>
        )}
      </TouchableOpacity>

      {/* Bottom view count */}
      <View className="absolute bottom-[40px] left-0 right-0 flex-row items-center justify-center px-4 py-6">
        <View className="flex-row items-center rounded-full bg-black/30 px-4 py-2">
          <Ionicons name="eye-outline" size={18} color="#fff" />
          <Text className="ml-2 font-medium text-white">{currentStory.viewers.length}</Text>
          <View className="mx-3 h-4 w-px bg-white/30" />
          <TouchableOpacity
            className="flex-row items-center"
            onPress={() => {
              // Swipe up to reply
              // In a real app, this would open a reply input
              alert('Reply to story');
            }}>
            <Ionicons name="arrow-up" size={18} color="#fff" />
            <Text className="ml-1 text-white">Reply</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}
