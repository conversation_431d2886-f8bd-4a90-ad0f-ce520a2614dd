import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  TextInput,
  ScrollView,
  Platform,
  Dimensions,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useNavigation } from 'expo-router';
import * as ImagePicker from 'expo-image-picker';
import { Ionicons, MaterialIcons, MaterialCommunityIcons } from '@expo/vector-icons';
import { useColorScheme } from '~/lib/useColorScheme';
import { Video, ResizeMode } from 'expo-av';
import { FileService } from '~/services/FileService';
import { UserStore } from '~/store/store';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default function CreateStoryScreen() {
  const navigation = useNavigation();
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const user = UserStore((state: any) => state.user);

  const [selectedMedia, setSelectedMedia] = useState<ImagePicker.ImagePickerAsset | null>(null);
  const [caption, setCaption] = useState('');
  const [textColor, setTextColor] = useState('#FFFFFF');
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);

  // Color options for text
  const colorOptions = [
    '#FFFFFF',
    '#000000',
    '#F44336',
    '#2196F3',
    '#FFEB3B',
    '#4CAF50',
    '#FF9800',
  ];

  const pickImage = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Sorry, we need camera roll permissions to make this work!'
        );
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images', 'videos'],
        allowsEditing: true,
        aspect: [9, 16],
        quality: 1,
        videoMaxDuration: 30, // 30 seconds max for stories
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setSelectedMedia(result.assets[0]);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick media');
    }
  };

  const openCamera = async () => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Sorry, we need camera permissions to make this work!');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ['images', 'videos'],
        allowsEditing: true,
        aspect: [9, 16],
        quality: 1,
        videoMaxDuration: 30, // 30 seconds max for stories
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setSelectedMedia(result.assets[0]);
      }
    } catch (error) {
      console.error('Error using camera:', error);
      Alert.alert('Error', 'Failed to take photo/video');
    }
  };

  const handlePost = async () => {
    if (!selectedMedia) {
      Alert.alert('Error', 'Please select an image or video for your story');
      return;
    }

    if (!user?.id) {
      Alert.alert('Error', 'User not found. Please try logging in again.');
      return;
    }

    setUploading(true);
    setLoading(true);

    try {
      // Create a File object from the selected media
      const fileExtension = selectedMedia.uri.split('.').pop() || '';
      const mimeType =
        selectedMedia.type === 'video' ? `video/${fileExtension}` : `image/${fileExtension}`;

      const file = {
        uri: selectedMedia.uri,
        type: mimeType,
        name: `story_${Date.now()}.${fileExtension}`,
      } as any;

      // Upload the story using FileService
      const response = await FileService.createStory([file], user.id.toString());

      if (response.success) {
        Alert.alert('Success', 'Story uploaded successfully!', [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]);
      } else {
        throw new Error(response.message || 'Failed to upload story');
      }
    } catch (error) {
      console.error('Error uploading story:', error);
      Alert.alert('Error', 'Failed to upload story. Please try again.');
    } finally {
      setUploading(false);
      setLoading(false);
    }
  };

  return (
    <View className={`flex-1 `} style={{ backgroundColor: colors.background }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />

      <View className="flex-row items-center justify-between px-4 pt-12 pb-4">
        <TouchableOpacity className="p-2" onPress={() => navigation.goBack()}>
          <Ionicons name="close" size={28} color={isDark ? '#fff' : '#000'} />
        </TouchableOpacity>
        <Text className={`font-medium text-lg ${isDark ? 'text-white' : 'text-black'}`}>
          Create Story
        </Text>
        <TouchableOpacity
          className={`rounded-full bg-violet-600 px-4 py-2 ${!selectedMedia || uploading ? 'opacity-60' : ''}`}
          onPress={handlePost}
          disabled={!selectedMedia || uploading}>
          {uploading ? (
            <View className="flex-row items-center">
              <ActivityIndicator size="small" color="#fff" />
              <Text className="ml-2 font-medium text-white">Uploading...</Text>
            </View>
          ) : (
            <Text className="font-medium text-white">Post</Text>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView
        className="flex-1"
        contentContainerStyle={{ flexGrow: 1, padding: 16 }}
        keyboardShouldPersistTaps="handled">
        {selectedMedia ? (
          <View
            className="relative flex-1 overflow-hidden rounded-xl"
            style={{ height: SCREEN_WIDTH * 1.8 }}>
            {selectedMedia.type === 'video' ? (
              <Video
                source={{ uri: selectedMedia.uri }}
                className="w-full h-full"
                resizeMode={ResizeMode.COVER}
                shouldPlay={true}
                isLooping={true}
                isMuted={false}
              />
            ) : (
              <Image
                source={{ uri: selectedMedia.uri }}
                className="w-full h-full"
                resizeMode="cover"
              />
            )}

            <View className="absolute left-0 right-0 px-4 bottom-20">
              <TextInput
                style={{
                  color: textColor,
                  textShadowColor: 'rgba(0,0,0,0.8)',
                  textShadowOffset: { width: 1, height: 1 },
                  textShadowRadius: 3,
                  fontSize: 18,
                  fontWeight: '600',
                }}
                placeholder="Add a caption..."
                placeholderTextColor="rgba(255,255,255,0.8)"
                value={caption}
                onChangeText={setCaption}
                multiline
                maxLength={100}
              />
            </View>

            <View className="absolute left-0 right-0 bottom-4">
              <ScrollView horizontal showsHorizontalScrollIndicator={false} className="px-4">
                {colorOptions.map((color) => (
                  <TouchableOpacity
                    key={color}
                    className={`mr-3 h-[30px] w-[30px] rounded-full border border-white/50 ${textColor === color ? 'border-[3px] border-violet-600' : ''}`}
                    style={{ backgroundColor: color }}
                    onPress={() => setTextColor(color)}
                  />
                ))}
              </ScrollView>
            </View>

            <TouchableOpacity
              className="absolute items-center justify-center w-10 h-10 rounded-full right-4 top-4 bg-black/60"
              onPress={pickImage}>
              <MaterialIcons name="edit" size={20} color="#fff" />
            </TouchableOpacity>
          </View>
        ) : (
          <View className="items-center justify-center flex-1 px-6">
            <Text
              className={`mb-4 text-center font-bold text-2xl ${isDark ? 'text-white' : 'text-black'}`}>
              Create New Story
            </Text>
            <Text
              className={`mb-10 text-center text-base ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
              Share a photo or video that will disappear after 24 hours
            </Text>

            <View className="flex-row justify-around w-full">
              <TouchableOpacity
                className={`h-[120px] w-[120px] items-center justify-center rounded-xl `}
                style={{ backgroundColor: colors.grey5 }}
                onPress={pickImage}>
                <MaterialCommunityIcons name="image-multiple" size={32} color="#5A4FCF" />
                <Text
                  className={`mt-3 font-medium text-base ${isDark ? 'text-white' : 'text-black'}`}>
                  Gallery
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                className={`h-[120px] w-[120px] items-center justify-center rounded-xl `}
                style={{ backgroundColor: colors.grey5 }}
                onPress={openCamera}>
                <Ionicons name="camera" size={32} color="#5A4FCF" />
                <Text
                  className={`mt-3 font-medium text-base ${isDark ? 'text-white' : 'text-black'}`}>
                  Camera
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </ScrollView>
    </View>
  );
}
