import { MaterialIcons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useCallback, useEffect, useState } from 'react';
import { View, Text, FlatList, TouchableOpacity, TextInput, ScrollView, Image } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { Container } from '~/components/Container';
import EventMarkerSheet from '~/components/Event/EventMarkerSheet';
import { useColorScheme } from '~/lib/useColorScheme';
import { EventProvider, useEvent } from '~/providers/MapProvider';
import { EventService } from '~/services/EventService';
import { UserStore } from '~/store/store';
import { EventType } from '~/types';

// Event Card Component
const EventCard = React.memo(
  ({
    item,
    onPress,
    colors,
  }: {
    item: EventType;
    onPress: (item: EventType) => void;
    colors: any;
  }) => {
    // Format time
    const formatTime = (dateString: string) => {
      const date = new Date(dateString);
      return format(date, 'h:mm a');
    };

    // Format date for display in the new design
    const formatDisplayDate = (dateString: string) => {
      const date = new Date(dateString);
      return `${format(date, 'EEE')}, ${format(date, 'MMM d')}`;
    };

    // Get event image or a placeholder based on event type
    const getEventImage = () => {
      if (item.coverImage) return item.coverImage;

      // Default placeholder images based on event type
      const placeholders: Record<string, string> = {
        Business:
          'https://images.unsplash.com/photo-1565398305935-49e5dcc5c9f6?q=80&w=1920&auto=format',
        Leisure:
          'https://images.unsplash.com/photo-1517457373958-b7bdd4587205?q=80&w=1920&auto=format',
        Entertainment:
          'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?q=80&w=1920&auto=format',
        Education:
          'https://images.unsplash.com/photo-1524178232363-1fb2b075b655?q=80&w=1920&auto=format',
      };

      return (
        placeholders[item.eventType] ||
        'https://images.unsplash.com/photo-1540575467063-178a50c2df87?q=80&w=1920&auto=format'
      );
    };

    return (
      <TouchableOpacity
        onPress={() => onPress(item)}
        activeOpacity={0.7}
        className="mx-4 mb-4 overflow-hidden rounded-xl border"
        style={{
          backgroundColor: colors.background,
          borderColor: colors.grey5,
        }}>
        {/* Event Image - Always show an image (either the event's cover or a placeholder) */}
        <View className="h-32 w-full">
          <Image source={{ uri: getEventImage() }} className="h-full w-full" resizeMode="cover" />
        </View>

        {/* Event Details */}
        <View className="p-3">
          {/* Date and Time */}
          <View className="mb-2">
            <Text className="font-medium text-sm" style={{ color: colors.grey }}>
              {formatDisplayDate(item.startDateTime)} • {formatTime(item.startDateTime)} GMT-4
            </Text>
          </View>

          {/* Title */}
          <Text
            style={{ color: colors.foreground }}
            className="font-bold text-lg"
            numberOfLines={2}>
            {item.title}
          </Text>

          {/* Location */}
          <View className="mt-1">
            <Text style={{ color: colors.grey }} className="text-sm" numberOfLines={1}>
              {item.location}
            </Text>
          </View>

          {/* Creator Followers */}
          <View className="mt-2 flex-row items-center">
            <MaterialIcons name="people" size={16} color={colors.grey} />
            <Text style={{ color: colors.grey }} className="ml-1 text-sm">
              {Math.floor(Math.random() * 5000)} creator followers
            </Text>
          </View>

          {/* Action Buttons */}
          <View className="mt-3 flex-row justify-end">
            <TouchableOpacity className="mr-2 p-2">
              <MaterialIcons name="share" size={22} color={colors.grey} />
            </TouchableOpacity>
            <TouchableOpacity className="p-2">
              <MaterialIcons name="favorite-border" size={22} color={colors.grey} />
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    );
  }
);

function DiscoverComponent() {
  const { colors, isDark } = useColorScheme();
  const insets = useSafeAreaInsets();
  const { filteredEvents, setSearchQuery, setSelectedEvent, categories } = useEvent();
  const initialUserData = UserStore((state) => state.user);

  // State for filters
  const [location] = useState('In New York');
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [timeFilter, setTimeFilter] = useState('Anytime');
  const [priceFilter, setPriceFilter] = useState('All'); // "All", "Free", "Paid"
  const [sortBy] = useState('relevance');
  const [suggestedEvents, setSuggestedEvents] = useState<EventType[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Time filter options
  const timeFilterOptions = ['Anytime', 'Today', 'This Week', 'This Month', 'This Weekend'];

  // Function to check if event matches time filter
  const matchesTimeFilter = useCallback((event: EventType, filter: string) => {
    if (filter === 'Anytime') return true;

    const eventDate = new Date(event.startDateTime);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    switch (filter) {
      case 'Today':
        const eventDateOnly = new Date(
          eventDate.getFullYear(),
          eventDate.getMonth(),
          eventDate.getDate()
        );
        return eventDateOnly.getTime() === today.getTime();

      case 'This Week':
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);
        weekEnd.setHours(23, 59, 59, 999);
        return eventDate >= weekStart && eventDate <= weekEnd;

      case 'This Month':
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        monthEnd.setHours(23, 59, 59, 999);
        return eventDate >= monthStart && eventDate <= monthEnd;

      case 'This Weekend':
        const saturday = new Date(today);
        saturday.setDate(today.getDate() + (6 - today.getDay()));
        const sunday = new Date(saturday);
        sunday.setDate(saturday.getDate() + 1);
        sunday.setHours(23, 59, 59, 999);
        return eventDate >= saturday && eventDate <= sunday;

      default:
        return true;
    }
  }, []);

  // Fetch event suggestions
  const fetchEventSuggestions = useCallback(async () => {
    if (!initialUserData?.id) return;

    setIsLoading(true);
    setError(null);
    try {
      const response = await EventService.getEventSuggestions(initialUserData.id);
      setSuggestedEvents(response.data || []);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch event suggestions');
      console.error('Error fetching event suggestions:', err);
    } finally {
      setIsLoading(false);
    }
  }, [initialUserData?.id]);

  // Filter the events based on all filters
  const getFilteredEvents = useCallback(() => {
    // Use suggested events if available, otherwise fall back to filteredEvents
    const eventsToFilter = suggestedEvents.length > 0 ? suggestedEvents : filteredEvents;
    let filtered = [...eventsToFilter];

    // Filter by price
    if (priceFilter === 'Free') {
      filtered = filtered.filter((event) => !event.isPaid);
    } else if (priceFilter === 'Paid') {
      filtered = filtered.filter((event) => event.isPaid);
    }

    // Filter by time
    if (timeFilter !== 'Anytime') {
      filtered = filtered.filter((event) => matchesTimeFilter(event, timeFilter));
    }

    // Filter by search text
    if (searchText) {
      const query = searchText.toLowerCase();
      filtered = filtered.filter(
        (event) =>
          event.title.toLowerCase().includes(query) ||
          event.description.toLowerCase().includes(query) ||
          event.location.toLowerCase().includes(query)
      );
    }

    return filtered;
  }, [suggestedEvents, filteredEvents, priceFilter, timeFilter, searchText, matchesTimeFilter]);

  // Apply category filter through the MapProvider
  const handleCategorySelect = useCallback(
    (category: string) => {
      setSelectedCategory(category);
      setSearchQuery(category);
    },
    [setSearchQuery]
  );

  // Handle event selection
  const handleSelectEvent = useCallback(
    (event: EventType) => {
      setSelectedEvent(event);
    },
    [setSelectedEvent]
  );

  // Render event item
  const renderEventItem = useCallback(
    ({ item }: { item: EventType }) => {
      return <EventCard item={item} onPress={handleSelectEvent} colors={colors} />;
    },
    [colors, handleSelectEvent]
  );

  // Make sure we always have events when the component mounts
  useEffect(() => {
    // Reset category filter to show all events
    if (selectedCategory !== 'All') {
      setSearchQuery('All');
      setSelectedCategory('All');
    }

    // Fetch event suggestions
    fetchEventSuggestions();
  }, [fetchEventSuggestions]);

  // Get the final filtered events
  const displayEvents = getFilteredEvents();

  return (
    <>
      <View className="flex-1 pt-12" style={{ backgroundColor: colors.background }}>
        <StatusBar style={isDark ? 'light' : 'dark'} />

        {/* Header Title */}
        <View className="flex-row items-center justify-center px-4 pb-4">
          <Text className={`font-medium text-xl ${isDark ? 'text-white' : 'text-black'}`}>
            Discover Events
          </Text>
        </View>

        {/* Search Bar */}
        <View className="mx-4 mb-4 mt-10">
          <View
            className="flex-row items-center rounded-lg px-3 py-2"
            style={{ backgroundColor: colors.grey5 }}>
            <MaterialIcons name="search" size={22} color={colors.grey} />
            <TextInput
              placeholder="Search for events..."
              placeholderTextColor={colors.grey}
              value={searchText}
              onChangeText={setSearchText}
              className="ml-2 flex-1 text-base"
              style={{ color: colors.foreground }}
            />
            {searchText ? (
              <TouchableOpacity onPress={() => setSearchText('')}>
                <MaterialIcons name="close" size={22} color={colors.grey} />
              </TouchableOpacity>
            ) : null}
          </View>
        </View>

        {/* Filter Buttons */}
        <View className="mb-4 flex-row px-4">
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {/* Categories */}
            {[...categories].map((category) => (
              <TouchableOpacity
                key={category}
                className="mr-3 flex-row items-center rounded-lg px-3 py-2"
                style={{
                  backgroundColor: selectedCategory === category ? colors.primary : colors.grey5,
                }}
                onPress={() => handleCategorySelect(category)}>
                <Text
                  className="font-medium"
                  style={{
                    color: selectedCategory === category ? '#fff' : colors.grey,
                  }}>
                  {category.charAt(0).toUpperCase() + category.slice(1).toLowerCase()}
                </Text>
              </TouchableOpacity>
            ))}

            {/* Price Filter */}
            <TouchableOpacity
              className="mr-3 flex-row items-center rounded-lg px-3 py-2"
              style={{
                backgroundColor: priceFilter !== 'All' ? colors.primary : colors.grey5,
              }}
              onPress={() => {
                // Toggle between All, Free, Paid
                if (priceFilter === 'All') setPriceFilter('Free');
                else if (priceFilter === 'Free') setPriceFilter('Paid');
                else setPriceFilter('All');
              }}>
              <Text
                className="font-medium"
                style={{
                  color: priceFilter !== 'All' ? '#fff' : colors.grey,
                }}>
                {priceFilter === 'All' ? 'Price' : priceFilter}
              </Text>
            </TouchableOpacity>

            {/* Time Filter */}
            <TouchableOpacity
              className="mr-3 flex-row items-center rounded-lg px-3 py-2"
              style={{
                backgroundColor: timeFilter !== 'Anytime' ? colors.primary : colors.grey5,
              }}
              onPress={() => {
                // Cycle through time filter options
                const currentIndex = timeFilterOptions.indexOf(timeFilter);
                const nextIndex = (currentIndex + 1) % timeFilterOptions.length;
                setTimeFilter(timeFilterOptions[nextIndex]);
              }}>
              <MaterialIcons
                name="access-time"
                size={18}
                color={timeFilter !== 'Anytime' ? '#fff' : colors.grey}
              />
              <Text
                className="ml-1 font-medium"
                style={{
                  color: timeFilter !== 'Anytime' ? '#fff' : colors.grey,
                }}>
                {timeFilter}
              </Text>
              <MaterialIcons
                name="keyboard-arrow-down"
                size={18}
                color={timeFilter !== 'Anytime' ? '#fff' : colors.grey}
              />
            </TouchableOpacity>
          </ScrollView>
        </View>

        {/* Events Count and Sort */}
        <View className="mb-2 flex-row items-center justify-between px-4">
          <Text className="font-medium" style={{ color: colors.foreground }}>
            {isLoading ? 'Loading...' : `${displayEvents.length} events`}
          </Text>

          <TouchableOpacity className="flex-row items-center">
            <Text className="mr-1 font-medium" style={{ color: colors.foreground }}>
              Sort by {sortBy}
            </Text>
            <MaterialIcons name="keyboard-arrow-down" size={18} color={colors.foreground} />
          </TouchableOpacity>
        </View>

        {/* Events List */}
        <FlatList
          data={displayEvents}
          renderItem={renderEventItem}
          keyExtractor={(item) => item.id.toString()}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingVertical: 10 }}
          refreshing={isLoading}
          onRefresh={fetchEventSuggestions}
          ListEmptyComponent={
            <View className="flex-1 items-center justify-center py-10">
              <MaterialIcons name="event-busy" size={60} color={colors.grey5} />
              <Text className="mt-4 text-center" style={{ color: colors.grey }}>
                {error ? error : 'No events found'}
              </Text>
              <TouchableOpacity
                className="mt-4 rounded-lg px-4 py-2"
                style={{ backgroundColor: colors.primary }}
                onPress={() => {
                  setSearchText('');
                  setPriceFilter('All');
                  setSelectedCategory('All');
                  setTimeFilter('Anytime');
                  setSearchQuery('All');
                  if (error) {
                    setError(null);
                    fetchEventSuggestions();
                  }
                }}>
                <Text className="font-medium text-white">
                  {error ? 'Retry' : 'Clear all filters'}
                </Text>
              </TouchableOpacity>
            </View>
          }
        />

        {/* Event Marker Sheet will be rendered by the EventMarkerSheet component */}
      </View>
      <EventMarkerSheet />
    </>
  );
}
export default function Discover() {
  return (
    <EventProvider>
      <DiscoverComponent />
    </EventProvider>
  );
}
