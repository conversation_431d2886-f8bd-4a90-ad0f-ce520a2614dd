import React, { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import { View, Text, Platform, SafeAreaView, Keyboard, TouchableOpacity } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import * as ImagePicker from 'expo-image-picker';
import { useRouter } from 'expo-router';
import { COLORS } from '../../../theme/colors';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useColorScheme } from '../../../lib/useColorScheme';
import ChatList from '../../../components/Chat/ChatList';
import ActiveChat from '../../../components/Chat/ActiveChat';
import NewChatSheet from '../../../components/Chat/NewChatSheet';
import CreateCommunityModal from '../../../components/Chat/CreateCommunityModal';
import BrowseCommunitiesModal from '../../../components/Chat/BrowseCommunitiesModal';
import BottomSheet, { BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import { ChatItem, Friend, Message } from '../../../types/chat_type';
import { GiftedChat, Actions } from 'react-native-gifted-chat';

// Mock data for communities and events
const dummyUserData = {
  _id: 1,
  name: 'Me',
  avatar: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
};

const dummyCommunities = [
  {
    id: 1,
    name: 'Photography Lovers',
    avatar: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
    memberCount: 124,
    lastMessage: 'Great shot! Where was that taken?',
    timestamp: '2h ago',
  },
  {
    id: 2,
    name: 'Local Meetups',
    avatar: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
    memberCount: 56,
    lastMessage: "I'll be there at 7pm",
    timestamp: 'Yesterday',
  },
  {
    id: 3,
    name: 'Fitness Group',
    avatar: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
    memberCount: 89,
    lastMessage: 'New workout plan posted!',
    timestamp: '3d ago',
  },
];

const dummyEvents = [
  {
    id: 1,
    name: 'Summer Music Festival',
    date: 'Aug 15',
    avatar: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
    attendeeCount: 320,
    lastMessage: "Who's going to the main stage?",
    timestamp: '5m ago',
    unread: 2,
  },
  {
    id: 2,
    name: 'Tech Conference 2023',
    date: 'Sep 20',
    avatar: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
    attendeeCount: 175,
    lastMessage: 'Panel discussion starts in 10 minutes',
    timestamp: '2h ago',
  },
  {
    id: 3,
    name: 'Art Exhibition',
    date: 'Jul 25',
    avatar: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
    attendeeCount: 82,
    lastMessage: 'Does anyone know if photos are allowed?',
    timestamp: 'Jan 20',
  },
];

const directChats = [
  {
    id: 1,
    name: 'John Doe',
    avatar: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
    lastMessage: 'Hey there!',
    timestamp: '4:41 PM',
    unread: 1,
  },
  {
    id: 2,
    name: 'Jane Smith',
    avatar: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
    lastMessage: 'See you tomorrow!',
    timestamp: 'Yesterday',
  },
];

// Mock messages
const generateMessages = (count, offset = 0) => {
  return Array(count)
    .fill(0)
    .map((_, i) => ({
      _id: i + offset,
      text: `Message ${i + offset}`,
      createdAt: new Date(Date.now() - (i + offset) * 60 * 1000),
      user: {
        _id: i % 2 === 0 ? 1 : 2,
        name: i % 2 === 0 ? 'Me' : 'John Doe',
        avatar:
          i % 2 === 0
            ? 'https://images.unsplash.com/photo-1540575467063-178a50c2df87'
            : 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
      },
    }));
};

// Enum for chat types
enum ChatType {
  DIRECT = 'direct',
  COMMUNITY = 'community',
  EVENT = 'event',
}

const Chat = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [chatType, setChatType] = useState<ChatType>(ChatType.EVENT);
  const [selectedChat, setSelectedChat] = useState<ChatItem | null>(null);
  const [myCommunities, setMyCommunities] = useState(dummyCommunities);
  const [showCreateCommunityModal, setShowCreateCommunityModal] = useState(false);
  const [showBrowseCommunitiesModal, setShowBrowseCommunitiesModal] = useState(false);
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const { colors, colorScheme, setColorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [keyboardVisible, setKeyboardVisible] = useState(false);

  // Reference for the bottom sheet
  const newChatSheetRef = useRef<BottomSheet>(null);
  // Define the snap points for the bottom sheet
  const snapPoints = useMemo(() => ['70%'], []);

  // Open the new chat bottom sheet
  const handleOpenNewChatSheet = useCallback(() => {
    newChatSheetRef.current?.expand();
  }, []);

  // Close the new chat bottom sheet
  const handleCloseNewChatSheet = useCallback(() => {
    newChatSheetRef.current?.close();
  }, []);

  useEffect(() => {
    // Reset messages when chat type changes
    if (selectedChat) {
      setMessages(generateMessages(15));
    }
  }, [selectedChat]);

  const handleSelectChat = (chat: ChatItem, type: 'direct' | 'community' | 'event') => {
    setChatType(type as ChatType);
    setSelectedChat(chat);
  };

  const handleSelectFriend = (friend: Friend) => {
    // Create a new chat item from the friend
    const newChat: ChatItem = {
      id: friend.id,
      name: friend.name,
      avatar: friend.avatar,
      lastMessage: '',
      timestamp: 'Just now',
    };

    // Close the bottom sheet
    handleCloseNewChatSheet();

    // Start a new chat
    handleSelectChat(newChat, 'direct');
  };

  const handleOpenCreateCommunity = () => {
    setShowCreateCommunityModal(true);
  };

  const handleCloseCreateCommunity = () => {
    setShowCreateCommunityModal(false);
  };

  const handleCreateCommunity = (name: string, description: string, imageUri: string) => {
    // Create new community and add to myCommunities
    const newCommunity = {
      id: Math.floor(Math.random() * 10000),
      name,
      avatar: imageUri,
      memberCount: 1,
      lastMessage: 'Welcome to the community!',
      timestamp: 'Just now',
    };

    setMyCommunities([newCommunity, ...myCommunities]);
    setShowCreateCommunityModal(false);
  };

  const handleOpenBrowseCommunities = () => {
    setShowBrowseCommunitiesModal(true);
  };

  const handleCloseBrowseCommunities = () => {
    setShowBrowseCommunitiesModal(false);
  };

  const handleJoinCommunity = (community: any) => {
    // Check if community is already joined
    const isJoined = myCommunities.some((c) => c.id === community.id);

    if (!isJoined) {
      // Add the joined community to myCommunities
      const newCommunity = {
        id: community.id,
        name: community.name,
        avatar: community.avatar,
        memberCount: community.memberCount,
        lastMessage: 'Welcome to the community!',
        timestamp: 'Just now',
      };

      setMyCommunities([newCommunity, ...myCommunities]);
    }

    setShowBrowseCommunitiesModal(false);
  };

  const onSend = useCallback((newMessages = []) => {
    setMessages((previousMessages) => GiftedChat.append(previousMessages, newMessages));
  }, []);

  const renderActions = (props) => {
    return (
      <Actions
        {...props}
        containerStyle={{
          marginLeft: 5,
          marginBottom: 8,
          justifyContent: 'center',
          alignItems: 'center',
          height: 36,
          width: 36,
          backgroundColor: colors.background,
        }}
        icon={() => (
          <Ionicons name="add-circle-outline" className="" size={40} color={colors.primary} />
        )}
        options={{
          'Choose From Library': async () => {
            try {
              const result = await ImagePicker.launchImageLibraryAsync({
                mediaTypes: ImagePicker.MediaTypeOptions.All,
                allowsEditing: true,
                quality: 0.8,
              });

              if (!result.canceled && result.assets?.[0]) {
                const uri = result.assets[0].uri;
                const newMessage = {
                  _id: Math.round(Math.random() * 1000000),
                  createdAt: new Date(),
                  user: dummyUserData,
                  image: uri,
                };
                onSend([newMessage]);
              }
            } catch (error) {
              console.log('Error picking image:', error);
            }
          },
          'Take a Photo': async () => {
            try {
              const { status } = await ImagePicker.requestCameraPermissionsAsync();
              if (status !== 'granted') {
                alert('Sorry, we need camera permissions to make this work!');
                return;
              }

              const result = await ImagePicker.launchCameraAsync({
                mediaTypes: ImagePicker.MediaTypeOptions.Images,
                allowsEditing: true,
                quality: 0.8,
              });

              if (!result.canceled && result.assets?.[0]) {
                const uri = result.assets[0].uri;
                const newMessage = {
                  _id: Math.round(Math.random() * 1000000),
                  createdAt: new Date(),
                  user: dummyUserData,
                  image: uri,
                };
                onSend([newMessage]);
              }
            } catch (error) {
              console.log('Error taking photo:', error);
            }
          },
          'Record a Video': async () => {
            try {
              const { status } = await ImagePicker.requestCameraPermissionsAsync();
              if (status !== 'granted') {
                alert('Sorry, we need camera permissions to make this work!');
                return;
              }

              const result = await ImagePicker.launchCameraAsync({
                mediaTypes: ImagePicker.MediaTypeOptions.Videos,
                allowsEditing: true,
                quality: 0.8,
              });

              if (!result.canceled && result.assets?.[0]) {
                const uri = result.assets[0].uri;
                const newMessage = {
                  _id: Math.round(Math.random() * 1000000),
                  createdAt: new Date(),
                  user: dummyUserData,
                  video: uri,
                };
                onSend([newMessage]);
              }
            } catch (error) {
              console.log('Error recording video:', error);
            }
          },
          Cancel: () => {},
        }}
      />
    );
  };

  // Tabs component
  const renderTabs = () => {
    return (
      <View
        className="flex-row justify-around border-b"
        style={{ borderBottomColor: 'rgba(150, 150, 150, 0.2)' }}>
        <TouchableOpacity
          className={`flex-1 items-center border-b-2 py-3.5 ${chatType === ChatType.DIRECT ? 'border-violet-600' : 'border-transparent'}`}
          onPress={() => setChatType(ChatType.DIRECT)}>
          <Text
            className={`font-medium text-xs ${chatType === ChatType.DIRECT ? 'opacity-100' : 'opacity-70'}`}
            style={{ color: chatType === ChatType.DIRECT ? colors.foreground : colors.grey }}>
            DIRECT
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          className={`flex-1 items-center border-b-2 py-3.5 ${chatType === ChatType.EVENT ? 'border-violet-600' : 'border-transparent'}`}
          onPress={() => setChatType(ChatType.EVENT)}>
          <Text
            className={`font-medium text-xs ${chatType === ChatType.EVENT ? 'opacity-100' : 'opacity-70'}`}
            style={{ color: chatType === ChatType.EVENT ? colors.foreground : colors.grey }}>
            EVENTS
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          className={`flex-1 items-center border-b-2 py-3.5 ${chatType === ChatType.COMMUNITY ? 'border-violet-600' : 'border-transparent'}`}
          onPress={() => setChatType(ChatType.COMMUNITY)}>
          <Text
            className={`font-medium text-xs ${chatType === ChatType.COMMUNITY ? 'opacity-100' : 'opacity-70'}`}
            style={{ color: chatType === ChatType.COMMUNITY ? colors.foreground : colors.grey }}>
            COMMUNITIES
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderBackdrop = useCallback(
    (props) => (
      <BottomSheetBackdrop {...props} disappearsOnIndex={-1} appearsOnIndex={0} opacity={0.5} />
    ),
    []
  );

  // Render chat lists based on type
  const renderChatLists = () => {
    let data = [];
    let renderType = 'direct';
    let headerTitle = '';
    let showAddButton = false;

    switch (chatType) {
      case ChatType.DIRECT:
        data = directChats;
        renderType = 'direct';
        headerTitle = 'Direct Messages';
        showAddButton = true;
        break;
      case ChatType.COMMUNITY:
        data = myCommunities;
        renderType = 'community';
        headerTitle = 'Communities';
        break;
      case ChatType.EVENT:
        data = dummyEvents;
        renderType = 'event';
        headerTitle = 'Event Chats';
        break;
    }

    return (
      <View className="flex-1" style={{ backgroundColor: colors.background }}>
        <View className="mt-10"></View>

        {renderTabs()}

        <ChatList
          data={data}
          type={renderType as 'direct' | 'community' | 'event'}
          onSelect={handleSelectChat}
          isDark={isDark}
          colors={colors}
          headerTitle={headerTitle}
          showAddButton={showAddButton}
          onAddPress={
            renderType === 'community' ? handleOpenCreateCommunity : handleOpenNewChatSheet
          }
          onBrowsePress={handleOpenBrowseCommunities}
        />
      </View>
    );
  };

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () =>
      setKeyboardVisible(true)
    );
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () =>
      setKeyboardVisible(false)
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  return (
    <SafeAreaView
      className="flex-1 pt-12"
      style={{
        backgroundColor: colors.background,
      }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      <View className="flex-row items-center justify-center px-4 pb-4">
        <Text className={`font-medium text-xl ${isDark ? 'text-white' : 'text-black'}`}>
          Messages
        </Text>
      </View>

      {selectedChat ? (
        <ActiveChat
          selectedChat={selectedChat}
          messages={messages}
          onSend={onSend}
          onBack={() => setSelectedChat(null)}
          renderActions={renderActions}
          colors={colors}
          isDark={isDark}
          user={dummyUserData}
        />
      ) : (
        renderChatLists()
      )}

      {/* Bottom Sheet for New Chat */}
      <BottomSheet
        ref={newChatSheetRef}
        index={-1}
        snapPoints={snapPoints}
        enablePanDownToClose
        backdropComponent={renderBackdrop}
        handleIndicatorStyle={{
          backgroundColor: isDark ? '#6b7280' : '#a1a1aa',
          width: 40,
        }}
        backgroundStyle={{
          backgroundColor: colors.background,
        }}>
        <NewChatSheet
          bottomSheetRef={newChatSheetRef}
          onSelectFriend={handleSelectFriend}
          colors={colors}
          isDark={isDark}
        />
      </BottomSheet>

      {/* Modal for Creating Community */}
      <CreateCommunityModal
        visible={showCreateCommunityModal}
        onClose={handleCloseCreateCommunity}
        onCreateCommunity={handleCreateCommunity}
        colors={colors}
        isDark={isDark}
      />

      {/* Modal for Browsing Communities */}
      <BrowseCommunitiesModal
        visible={showBrowseCommunitiesModal}
        onClose={handleCloseBrowseCommunities}
        onJoinCommunity={handleJoinCommunity}
        colors={colors}
      />
    </SafeAreaView>
  );
};

export default Chat;
