import React, { useState } from 'react';
import { View, Text, TouchableOpacity, SafeAreaView, Platform } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useRouter } from 'expo-router';
import { AntDesign, FontAwesome5 } from '@expo/vector-icons';
import { useColorScheme } from '~/lib/useColorScheme';

// Import step components
import SignupFormStep from '~/components/Auth/SignupFormStep';
import EmailVerificationStep from '~/components/Auth/EmailVerificationStep';
import UsernameStep from '~/components/Auth/UsernameStep';
import GenderStep from '~/components/Auth/GenderStep';
import InterestsStep from '~/components/Auth/InterestsStep';
import EventTypesStep from '~/components/Auth/EventTypesStep';
import CompletionStep from '~/components/Auth/CompletionStep';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { AuthService } from '~/services/AuthService';
import { Toast } from 'toastify-react-native';

// Define the user data interface
export interface UserData {
  fullName: string;
  email: string;
  password: string;
  termsAccepted: boolean;
  phoneNumber: string;
  username: string;
  gender: string;
  interests: string[];
  eventPreferences: string[];
  permissionsGranted: boolean;
}

export default function SignupScreen() {
  const router = useRouter();
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 6; // 6 main steps in the signup flow

  // Initialize user data
  const [userData, setUserData] = useState<UserData>({
    fullName: '',
    email: '',
    password: '',
    phoneNumber: '',
    termsAccepted: false,
    username: '',
    gender: '',
    interests: [],
    eventPreferences: [],
    permissionsGranted: false,
  });

  // Simulate sending verification code
  const sendVerificationCode = async () => {
    try {
      const data = await AuthService.initiateRegistration(userData.email, userData.phoneNumber);
      return data;
    } catch (error) {
      return error;
    }
  };

  // Handle next step
  const nextStep = () => {
    if (currentStep < totalSteps) {
      // If moving to email verification step, send the code
      if (currentStep === 1) {
        sendVerificationCode();
      }
      setCurrentStep(currentStep + 1);
    } else {
      // Complete signup
      completeSignup();
    }
  };

  // Handle previous step
  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      router.back();
    }
  };

  // Update user data
  const updateUserData = (data: Partial<UserData>) => {
    setUserData({ ...userData, ...data });
  };

  // Complete signup process
  const completeSignup = () => {
    // Add your signup logic here
    console.log('Signup completed with data:', userData);

    // Navigate to the main app
    router.push('/(drawer)/(tabs)');
  };

  // Render profile setup steps (steps 3-6)
  const renderProfileSetupStep = () => {
    switch (currentStep) {
      /*   case 3: // Username step
        return (
          <UsernameStep userData={userData} updateUserData={updateUserData} onNext={nextStep} />
        ); */
      case 3: // Combined interests step (gender + interests)
        return (
          <View className="flex-1">
            <GenderStep userData={userData} updateUserData={updateUserData} onNext={nextStep} />
          </View>
        );
      case 4: // Event preferences step
        return (
          <View className="flex-1">
            <EventTypesStep userData={userData} updateUserData={updateUserData} onNext={nextStep} />
          </View>
        );
      case 5: // Event preferences step
        return (
          <View className="flex-1">
            <InterestsStep userData={userData} updateUserData={updateUserData} onNext={nextStep} />
          </View>
        );
      case 6: // Permissions step
        return (
          <CompletionStep userData={userData} updateUserData={updateUserData} onNext={nextStep} />
        );
      default:
        return null;
    }
  };

  // Render current step
  const renderStep = () => {
    switch (currentStep) {
      case 1: // Initial signup form
        return (
          <SignupFormStep
            userData={userData}
            updateUserData={updateUserData}
            onNext={nextStep}
            router={router}
          />
        );
      case 2: // Email verification
        return (
          <EmailVerificationStep
            userData={userData}
            updateUserData={updateUserData}
            onNext={nextStep}
            resendCode={sendVerificationCode}
            onBack={prevStep}
          />
        );
      case 3:
      case 4:
      case 5:
      case 6:
        return renderProfileSetupStep();
      default:
        return null;
    }
  };

  // Get step title
  const getStepTitle = () => {
    switch (currentStep) {
      case 1:
        return 'Create Account';
      case 2:
        return 'Verify Identity';
      case 3:
      case 4:
      case 5:
        return 'Tell us who you are';
      case 6:
        return 'Start Browsing';
      default:
        return 'Sign Up';
    }
  };
  const insets = useSafeAreaInsets();
  return (
    <View className="flex-1" style={{ backgroundColor: colors.background }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />

      <View
        className="flex-row items-center justify-between px-3 pb-6 "
        style={{ paddingTop: Platform.OS === 'ios' ? insets.top : insets.top + 15 }}>
        <TouchableOpacity
          onPress={prevStep}
          className="h-10 w-10 items-center justify-center rounded-full">
          <AntDesign name="arrowleft" size={24} color={colors.foreground} />
        </TouchableOpacity>
        <Text className="font-bold text-lg" style={{ color: colors.foreground }}>
          {getStepTitle()}
        </Text>
        {/*   <View className="flex-row items-center justify-center gap-1.5 px-6">
          {Array.from({ length: totalSteps }).map((_, index) => (
            <View
              key={index}
              className="h-2 rounded"
              style={{
                backgroundColor: index < currentStep ? colors.primary : colors.grey,
                width: index < currentStep ? 24 : 8,
              }}
            />
          ))}
        </View> */}
        <View className="self-center px-6 ">
          <Text className="font-sans text-sm" style={{ color: colors.grey }}>
            Step {currentStep} of {totalSteps}
          </Text>
        </View>
        {/*    <View className="flex-row items-center justify-center gap-1 px-4">
          {Array.from({ length: totalSteps }).map((_, index) => (
            <View
              key={index}
              className="h-2 rounded"
              style={{
                backgroundColor: index < currentStep ? colors.primary : colors.grey,
                width: index < currentStep ? 16 : 8,
              }}
            />
          ))}
        </View> */}
      </View>

      <View className="flex-1 ">{renderStep()}</View>
    </View>
  );
}
