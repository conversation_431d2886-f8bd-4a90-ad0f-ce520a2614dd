import React, { useRef } from 'react';
import {
  View,
  TouchableWithoutFeedback,
  Keyboard,
  Platform,
  Pressable,
  TextInput,
  Alert,
  Image,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useRouter } from 'expo-router';
import { AntDesign, FontAwesome5 } from '@expo/vector-icons';
import { useColorScheme } from '~/lib/useColorScheme';
import { z } from 'zod';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { AuthService } from '@/services/AuthService';
import { Toast } from 'toastify-react-native';
import * as Location from 'expo-location';
import * as ImagePicker from 'expo-image-picker';
import { KeyboardAwareScrollView, KeyboardProvider } from 'react-native-keyboard-controller';

import {
  FormControl,
  FormControlError,
  FormControlErrorText,
  FormControlLabel,
  FormControlLabelText,
} from '@/components/ui/form-control';
import { Text } from '~/components/nativewindui/Text';
import { HStack } from '~/components/ui/hstack';
import { Box } from '~/components/ui/box';
import { VStack } from '~/components/ui/vstack';
import { Input, InputField, InputSlot } from '~/components/ui/input';
import { Button, ButtonText } from '@/components/ui/button';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { AuthStore } from '~/store/store';

// Define validation schema with zod
const loginSchema = z.object({
  email: z.string().email('Please enter a valid email'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

type LoginFormData = z.infer<typeof loginSchema>;

export default function LoginScreen() {
  const router = useRouter();
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [showPassword, setShowPassword] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);

  const emailInputRef = useRef<TextInput>(null);
  const passwordInputRef = useRef<TextInput>(null);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const CheckPermissions = async () => {
    const location = await Location.getForegroundPermissionsAsync();
    const image = await ImagePicker.getMediaLibraryPermissionsAsync();
    if (location.status !== 'granted' || image.status !== 'granted') {
      // Permissions NOT granted - disable permissions in store
      (AuthStore.getState() as { disable: () => void }).disable();
      return true; // return true means permissions are missing
    } else {
      // Permissions ARE granted - enable permissions in store
      (AuthStore.getState() as { enable: () => void }).enable();
      return false; // return false means permissions are complete
    }
  };

  const handleLogin = async (data: LoginFormData) => {
    Keyboard.dismiss();
    setIsLoading(true);

    try {
      const response = await AuthService.login(data.email, data.password);

      if (response.body.user.setupComplete) {
        const permissions = await CheckPermissions();
        if (permissions) {
          router.push('/Auth/permissions');
        } else {
          router.replace('/(drawer)/(tabs)');
        }
      } else {
        router.push('/Auth/profileSetup');
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Login Failed',
        text2: error.message,
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } finally {
      setIsLoading(false);
    }
  };
  const insets = useSafeAreaInsets();

  return (
    <KeyboardProvider>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
        <View className={`flex-1 `} style={{ backgroundColor: colors.background }}>
          <StatusBar style={isDark ? 'light' : 'dark'} />

          <KeyboardAwareScrollView
            className="flex-1 px-3 pb-6"
            style={{ paddingTop: Platform.OS === 'ios' ? insets.top : insets.top + 15 }}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ flexGrow: 1 }}
            enableOnAndroid={true}
            extraScrollHeight={20}>
            <HStack className="mb-12 items-center justify-between">
              <Pressable
                onPress={() => router.push('/Auth/onboarding')}
                className="h-10 w-10 items-center justify-center rounded-full">
                <AntDesign name="arrowleft" size={24} color={colors.foreground} />
              </Pressable>
              <Text className="font-bold text-lg" style={{ color: colors.foreground }}>
                Login
              </Text>
              <Box className="w-10" />
            </HStack>

            <Box className="mb-6 items-center">
              {isDark ? (
                <Image source={require('../../assets/dark_theme_logo.png')} className="h-24 w-24" />
              ) : (
                <Image
                  source={require('../../assets/light_theme_logo.png')}
                  className="h-24 w-24"
                />
              )}
            </Box>

            <Text
              className="mb-2 text-center font-bold text-2xl"
              style={{ color: colors.foreground }}>
              Welcome
            </Text>
            <Text
              className="mb-10 px-3 text-center font-sans text-base opacity-70"
              style={{ color: colors.grey }}>
              Login to discover events and people
            </Text>

            <VStack space="lg" className="mb-6 px-3">
              <FormControl isInvalid={!!errors.email}>
                <Controller
                  name="email"
                  control={control}
                  render={({ field: { onChange, onBlur, value } }) => (
                    <Input
                      variant="outline"
                      className={`h-14 rounded-xl border-0 `}
                      style={{ backgroundColor: colors.grey5 }}>
                      <InputField
                        ref={emailInputRef}
                        placeholder="Email Address"
                        onBlur={onBlur}
                        onChangeText={onChange}
                        value={value}
                        autoCapitalize="none"
                        keyboardType="email-address"
                        returnKeyType="next"
                        onSubmitEditing={() => passwordInputRef.current?.focus()}
                        blurOnSubmit={false}
                        className={`font-medium placeholder:text-[${colors.grey}] ${isDark ? 'text-white' : 'text-black'}`}
                        placeholderTextColor={isDark ? colors.grey : colors.grey}
                      />
                    </Input>
                  )}
                />
                <FormControlError>
                  <FormControlErrorText className="text-red-500">
                    {errors.email?.message}
                  </FormControlErrorText>
                </FormControlError>
              </FormControl>

              <FormControl isInvalid={!!errors.password}>
                <Controller
                  name="password"
                  control={control}
                  render={({ field: { onChange, onBlur, value } }) => (
                    <Input
                      variant="outline"
                      className={`h-14 rounded-xl border-0 `}
                      style={{ backgroundColor: colors.grey5 }}>
                      <InputField
                        ref={passwordInputRef}
                        placeholder="Password"
                        onBlur={onBlur}
                        onChangeText={onChange}
                        value={value}
                        secureTextEntry={!showPassword}
                        returnKeyType="done"
                        onSubmitEditing={handleSubmit(handleLogin)}
                        className={`font-medium placeholder:text-[${colors.grey}] ${isDark ? 'text-white' : 'text-black'}`}
                        placeholderTextColor={isDark ? colors.grey : colors.grey}
                      />
                      <InputSlot className="pr-3">
                        <Pressable onPress={() => setShowPassword(!showPassword)}>
                          <FontAwesome5
                            name={showPassword ? 'eye-slash' : 'eye'}
                            size={18}
                            color={isDark ? '#9ca3af' : '#6b7280'}
                          />
                        </Pressable>
                      </InputSlot>
                    </Input>
                  )}
                />
                <FormControlError>
                  <FormControlErrorText className="text-red-500">
                    {errors.password?.message}
                  </FormControlErrorText>
                </FormControlError>
              </FormControl>
            </VStack>

            <Pressable
              className="mb-6 self-end px-3"
              onPress={() => router.push('/Auth/forgotPassword')}>
              <Text
                className="font-medium text-sm"
                style={{
                  color: isDark ? colors.primary : colors.primary,
                }}>
                Forgot Password?
              </Text>
            </Pressable>

            <Button
              className={`mx-3 mt-4  h-14  flex-row rounded-xl ${isDark ? 'bg-violet-700' : 'bg-violet-600'}`}
              onPress={handleSubmit(handleLogin)}
              isDisabled={isLoading}>
              <ButtonText className="font-bold text-white">
                {isLoading ? 'Logging in...' : 'Login'}
              </ButtonText>
            </Button>

            <HStack className="mt-10 justify-center">
              <Text className="text-sm" style={{ color: colors.foreground }}>
                Don't have an account?
              </Text>
              <Pressable onPress={() => router.push('/Auth/signup')}>
                <Text
                  className="ml-1 font-medium text-sm"
                  style={{
                    color: isDark ? colors.primary : colors.primary,
                  }}>
                  Sign up
                </Text>
              </Pressable>
            </HStack>
          </KeyboardAwareScrollView>
        </View>
      </TouchableWithoutFeedback>
    </KeyboardProvider>
  );
}
