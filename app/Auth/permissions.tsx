import React, { useState, useEffect } from 'react';
import { View, Text, SafeAreaView, Alert, TouchableOpacity, Platform } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useRouter } from 'expo-router';
import { useColorScheme } from '~/lib/useColorScheme';
import * as Location from 'expo-location';
import * as ImagePicker from 'expo-image-picker';
import { AntDesign, FontAwesome5 } from '@expo/vector-icons';
import { Button, ButtonText } from '@/components/ui/button';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Toast } from 'toastify-react-native';
import { AuthStore } from '~/store/store';

export default function PermissionsScreen() {
  const router = useRouter();
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [locationPermission, setLocationPermission] = useState<'granted' | 'denied' | 'pending'>(
    'pending'
  );
  const [mediaPermission, setMediaPermission] = useState<'granted' | 'denied' | 'pending'>(
    'pending'
  );
  const [isLoading, setIsLoading] = useState(false);

  // Check for existing permissions on component mount
  useEffect(() => {
    checkLocationPermission();
    checkMediaPermission();
  }, []);

  const checkLocationPermission = async () => {
    const { status } = await Location.getForegroundPermissionsAsync();
    setLocationPermission(status === 'granted' ? 'granted' : 'denied');
  };

  const checkMediaPermission = async () => {
    const { status } = await ImagePicker.getMediaLibraryPermissionsAsync();
    setMediaPermission(status === 'granted' ? 'granted' : 'denied');
  };

  const requestLocationPermission = async () => {
    const { status } = await Location.requestForegroundPermissionsAsync();
    setLocationPermission(status === 'granted' ? 'granted' : 'denied');

    if (status !== 'granted') {
      Toast.show({
        type: 'warn',
        text1: 'Location Permission Required',
        text2:
          'Location permission is required to use this app. Please enable it in your device settings.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    }
  };

  const requestMediaPermission = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    setMediaPermission(status === 'granted' ? 'granted' : 'denied');

    if (status !== 'granted') {
      Toast.show({
        type: 'warn',
        text1: 'Media Permission Required',
        text2:
          'Media library permission is required to use this app. Please enable it in your device settings.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    }
  };

  const handleContinue = () => {
    if (locationPermission !== 'granted' || mediaPermission !== 'granted') {
      Toast.show({
        type: 'warn',
        text1: 'Permissions Required',
        text2: 'You need to grant all permissions to continue using the app.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
      return;
    }
    (AuthStore.getState() as { enable: () => void }).enable();
    setIsLoading(true);
    router.replace('/(drawer)/(tabs)');
  };
  const insets = useSafeAreaInsets();
  return (
    <SafeAreaView
      className="flex-1"
      style={{
        backgroundColor: colors.background,
        paddingTop: Platform.OS === 'ios' ? insets.top : insets.top + 15,
      }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />

      <View className="mt-3 flex-row items-center justify-between px-3">
        <TouchableOpacity
          onPress={() => router.back()}
          className="flex h-10 w-10 flex-row items-center justify-center rounded-full">
          <AntDesign name="close" size={24} color={colors.foreground} />
        </TouchableOpacity>

        <View className="px-6 ">
          <Text className="text-sm text-gray-500"></Text>
        </View>
      </View>
      <View className="px-6 pb-6 pt-6">
        <Text className="mb-4 font-bold text-2xl" style={{ color: colors.foreground }}>
          App Permissions
        </Text>

        <Text className="font-base mb-6" style={{ color: colors.grey }}>
          Before you can use all features of the app, we need the following permissions:
        </Text>
      </View>

      <View className="flex-1 px-6">
        {/*   <View className="p-4 mb-6 bg-yellow-100 rounded-lg">
          <Text className="text-base text-yellow-800">
            <Text className="font-bold">Important:</Text> These permissions are required to use the
            app. Without them, you won't be able to continue.
          </Text>
        </View>
 */}
        <View className="mb-6 space-y-8">
          <View
            className={`mb-4 rounded-xl border p-4 ${
              locationPermission === 'granted'
                ? 'border-green-500 bg-green-50'
                : 'border-gray-300 bg-gray-50'
            }`}>
            <View className="mb-2 flex-row items-center">
              <FontAwesome5
                name="map-marker-alt"
                size={18}
                color={locationPermission === 'granted' ? '#10b981' : '#6b7280'}
              />
              <Text className="ml-2 font-medium">Location Permission</Text>
              {locationPermission === 'granted' && (
                <View className="ml-auto">
                  <FontAwesome5 name="check-circle" size={18} color="#10b981" />
                </View>
              )}
            </View>
            <Text className="mb-3 text-sm text-gray-600">
              Required to show nearby events, find people around you, and provide accurate
              directions.
            </Text>
            {locationPermission !== 'granted' && (
              <Button onPress={requestLocationPermission} className="h-10 rounded-lg bg-violet-600">
                <ButtonText className="text-sm text-white">Allow Location Access</ButtonText>
              </Button>
            )}
          </View>

          <View
            className={`rounded-xl border p-4  ${
              mediaPermission === 'granted'
                ? 'border-green-500 bg-green-50'
                : 'border-gray-300 bg-gray-50'
            }`}>
            <View className="mb-2 flex-row items-center">
              <FontAwesome5
                name="images"
                size={18}
                color={mediaPermission === 'granted' ? '#10b981' : '#6b7280'}
              />
              <Text className="ml-2 font-medium">Media Access</Text>
              {mediaPermission === 'granted' && (
                <View className="ml-auto">
                  <FontAwesome5 name="check-circle" size={18} color="#10b981" />
                </View>
              )}
            </View>
            <Text className="mb-3 text-sm text-gray-600">
              Required to upload profile photos, share event pictures, and save event tickets.
            </Text>
            {mediaPermission !== 'granted' && (
              <Button onPress={requestMediaPermission} className="h-10 rounded-lg bg-violet-600">
                <ButtonText className="text-sm text-white">Allow Media Access</ButtonText>
              </Button>
            )}
          </View>
        </View>
      </View>

      <View className="px-6 pb-8">
        <Button
          className={`h-14 flex-row rounded-xl ${
            locationPermission !== 'granted' || mediaPermission !== 'granted'
              ? isDark
                ? 'bg-gray-700'
                : 'bg-gray-300'
              : isDark
                ? 'bg-violet-700'
                : 'bg-violet-600'
          }`}
          onPress={handleContinue}
          isDisabled={
            locationPermission !== 'granted' || mediaPermission !== 'granted' || isLoading
          }>
          <ButtonText className="text-white">
            {isLoading ? 'Processing...' : 'Continue to App'}
          </ButtonText>
        </Button>
      </View>
    </SafeAreaView>
  );
}
