import { coodinates, MapTsType, ViewModeType } from './map_type';
import { Person } from './people_type';

interface LocationData {
  coordinates: [number, number];
  name: string;
  address: string;
}

interface TicketLevel {
  type: string;
  quantity: number;
  price: number;
}

interface TicketSetup {
  hasLevels: boolean;
  totalTickets: number | null;
  price: number | null;
  levels: TicketLevel[];
}

export interface EventType {
  id: number;
  title: string;
  description: string;
  location: string;
  locationData: LocationData;
  coverImage: string | null;
  startDateTime: string;
  endDateTime: string;
  eventType: string;
  visibility: string;
  isPaid: boolean;
  ticketSetup: TicketSetup;
  owners: string[];
  currency: string;
}

export interface EventContextType {
  selectedEvent: EventType | null;
  setSelectedEvent: (event: EventType | null) => void;
  filteredEvents: EventType[];
  setSearchQuery: (item: string) => void;
  categories: string[];
  searchQuery: string;
  cameraRef: any;
  zoomToLocation: (coodinates: coodinates, zoomLevel?: number, duration?: number) => void;
  resetToUserLocation: () => void;
  followUserLocation: boolean;
  setFollowUserLocation: (value: boolean) => void;
  ViewMode: ViewModeType;
  setViewMode: (mode: ViewModeType) => void;
  MapType: MapTsType;
  setMapType: (type: MapTsType) => void;
  People: Person[];
  zoomLevel: number;
  setZoomLevel: (zoom: number) => void;
  userId: number | null;
  setUserId: (userId: number | null) => void;
  setPeople: (people: Person[]) => void;
  personProfileSheetRef: React.RefObject<any>;
  isPersonProfileSheetOpen: boolean;
  setIsPersonProfileSheetOpen: (isOpen: boolean) => void;
  // New API-related properties
  isLoadingEvents: boolean;
  isLoadingCategories: boolean;
  eventsError: string | null;
  categoriesError: string | null;
  fetchEvents: (queryParams?: any) => Promise<EventType[]>;
  fetchCategories: () => Promise<string[]>;
}
