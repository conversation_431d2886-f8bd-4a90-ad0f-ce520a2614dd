export interface StoryViewer {
  id: number;
  name: string;
  profilePhoto: string;
  viewedAt: string; // ISO date string
}

export interface Story {
  id: number;
  userId: number;
  userName: string;
  userProfilePhoto: string;
  mediaUrl: string;
  mediaType: 'image' | 'video';
  caption?: string;
  createdAt: string; // ISO date string
  expiresAt: string; // ISO date string
  viewers: StoryViewer[];
  textColor: string;
}

export interface UserWithStory {
  userId: number;
  hasActiveStory: boolean;
  latestStoryCreatedAt: string; // ISO date string
}
