export interface ChatItem {
  id: number;
  name: string;
  avatar: string;
  lastMessage?: string;
  timestamp: string;
  unread?: number;
  memberCount?: number;
  attendeeCount?: number;
  date?: string;
}

export interface Message {
  _id: number;
  text: string;
  createdAt: Date;
  user: {
    _id: number;
    name: string;
    avatar: string;
  };
  image?: string;
  video?: string;
}

export interface Friend {
  id: number;
  name: string;
  avatar: string;
  status: 'online' | 'offline';
}
