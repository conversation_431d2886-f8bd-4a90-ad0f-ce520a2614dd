import React, { useRef, forwardRef, useImperativeHandle, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useColorScheme } from '~/lib/useColorScheme';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import BottomSheet, {
  BottomSheetView,
  BottomSheetBackdrop,
  BottomSheetModal,
} from '@gorhom/bottom-sheet';
import { RenderBackdrop } from '../RenderBackdrop';
import * as ImagePicker from 'expo-image-picker';
import { FileService } from '~/services/FileService';
import { UserStore } from '~/store/store';
import { Toast } from 'toastify-react-native';

export interface PhotoBottomSheetProps {
  currentPhoto: string;
  onPhotoSelected: (uri: string) => void;
  userId: string;
}

export interface PhotoBottomSheetHandle {
  present: () => void;
  dismiss: () => void;
}

const PhotoUploadBottomSheet = forwardRef<PhotoBottomSheetHandle, PhotoBottomSheetProps>(
  ({ currentPhoto, onPhotoSelected, userId }, ref) => {
    const bottomSheetModalRef = useRef<BottomSheetModal>(null);
    const { colorScheme, colors } = useColorScheme();
    const isDark = colorScheme === 'dark';
    const [isUploading, setIsUploading] = useState(false);

    useImperativeHandle(ref, () => ({
      present: () => {
        bottomSheetModalRef.current?.expand();
      },
      dismiss: () => bottomSheetModalRef.current?.close(),
    }));

    const uploadImageFile = async (uri: string) => {
      try {
        setIsUploading(true);

        // Create file object from URI
        const filename = uri.split('/').pop() || 'image.jpg';
        const fileType = filename.split('.').pop() || 'jpg';

        const file = {
          uri,
          type: `image/${fileType}`,
          name: filename,
        } as any;

        const response = await FileService.uploadManyImages([file], userId, 'PROFILE_PICTURE');
        onPhotoSelected(uri);
        (UserStore.getState() as { setUser: (data: any) => void }).setUser(response.body);
        bottomSheetModalRef.current?.close();
        Toast.show({
          type: 'success',
          text1: 'Profile Photo Updated',
          text2: 'Your profile photo has been updated.',
          position: 'bottom',
          theme: isDark ? 'dark' : 'light',
          backgroundColor: colors.background,
          autoHide: true,
        });
      } catch (error) {
        console.error('Image upload error:', error);
        Toast.show({
          type: 'error',
          text1: 'Upload Error',
          text2: 'Failed to upload image. Please try again.',
          position: 'bottom',
          theme: isDark ? 'dark' : 'light',
          backgroundColor: colors.background,
          autoHide: true,
        });
      } finally {
        setIsUploading(false);
      }
    };

    const takePhoto = async () => {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert('Permission required', 'Camera permission is required to take photos');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.7,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        await uploadImageFile(result.assets[0].uri);
      }
    };

    const chooseFromLibrary = async () => {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert('Permission required', 'Gallery permission is required to select photos');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.7,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        await uploadImageFile(result.assets[0].uri);
      }
    };

    const removePhoto = () => {
      Alert.alert('Remove Profile Photo', 'Are you sure you want to remove your profile photo?', [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            // Use default avatar or placeholder
            onPhotoSelected(
              'https://www.gravatar.com/avatar/00000000000000000000000000000000?d=mp&f=y'
            );
            bottomSheetModalRef.current?.close();
          },
        },
      ]);
    };

    return (
      <BottomSheet
        ref={bottomSheetModalRef}
        index={-1}
        snapPoints={['60%']}
        backdropComponent={RenderBackdrop}
        backgroundStyle={{
          backgroundColor: colors.background,
          borderTopLeftRadius: 24,
          borderTopRightRadius: 24,
        }}
        handleIndicatorStyle={{
          backgroundColor: isDark ? '#555' : '#999',
          width: 40,
        }}>
        <BottomSheetView className="flex-1 px-5 pt-2">
          <View className="flex-row items-center justify-between mb-5">
            <Text className={`text-xl font-semibold ${isDark ? 'text-white' : 'text-black'}`}>
              Profile Photo
            </Text>
            <TouchableOpacity className="p-2" onPress={() => bottomSheetModalRef.current?.close()}>
              <Ionicons name="close" size={24} color={isDark ? '#fff' : '#000'} />
            </TouchableOpacity>
          </View>

          <View className="flex-row justify-center mb-6">
            <Image source={{ uri: currentPhoto }} className="w-20 h-20 rounded-full" />
          </View>

          <View className="flex gap-3">
            <TouchableOpacity
              onPress={takePhoto}
              disabled={isUploading}
              className={`h-14 flex-row items-center rounded-lg px-4 py-3 ${isUploading ? 'opacity-50' : ''}`}
              style={{ backgroundColor: colors.grey5 }}>
              {isUploading ? (
                <ActivityIndicator
                  size="small"
                  color={isDark ? '#fff' : '#000'}
                  style={{ marginRight: 12 }}
                />
              ) : (
                <Ionicons
                  name="camera"
                  size={22}
                  color={isDark ? '#fff' : '#000'}
                  style={{ marginRight: 12 }}
                />
              )}
              <Text className={`font-medium ${isDark ? 'text-white' : 'text-black'}`}>
                {isUploading ? 'Uploading...' : 'Take Photo'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={chooseFromLibrary}
              disabled={isUploading}
              className={`h-14 flex-row items-center rounded-lg px-4 py-3 ${isUploading ? 'opacity-50' : ''}`}
              style={{ backgroundColor: colors.grey5 }}>
              {isUploading ? (
                <ActivityIndicator
                  size="small"
                  color={isDark ? '#fff' : '#000'}
                  style={{ marginRight: 12 }}
                />
              ) : (
                <MaterialCommunityIcons
                  name="image"
                  size={22}
                  color={isDark ? '#fff' : '#000'}
                  style={{ marginRight: 12 }}
                />
              )}
              <Text className={`font-medium ${isDark ? 'text-white' : 'text-black'}`}>
                {isUploading ? 'Uploading...' : 'Choose from Library'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={removePhoto}
              disabled={isUploading}
              className={`h-14 flex-row items-center rounded-lg bg-red-500 px-4 py-3 ${isUploading ? 'opacity-50' : ''}`}>
              <Ionicons name="trash" size={22} color="#fff" style={{ marginRight: 12 }} />
              <Text className="font-medium text-white">Remove Current Photo</Text>
            </TouchableOpacity>
          </View>
        </BottomSheetView>
      </BottomSheet>
    );
  }
);

export default PhotoUploadBottomSheet;
