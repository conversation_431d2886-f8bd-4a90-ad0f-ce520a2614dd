import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, Alert, Platform } from 'react-native';
import { useColorScheme } from '~/lib/useColorScheme';
import { UserData } from '~/app/Auth/signup';
import { Button, ButtonText } from '@/components/ui/button';
import { FontAwesome5 } from '@expo/vector-icons';
import * as Location from 'expo-location';
import * as ImagePicker from 'expo-image-picker';

interface PermissionsStepProps {
  userData: UserData;
  updateUserData: (data: Partial<UserData>) => void;
  onNext: () => void;
}

export default function PermissionsStep({
  userData,
  updateUserData,
  onNext,
}: PermissionsStepProps) {
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [locationPermission, setLocationPermission] = useState<'granted' | 'denied' | 'pending'>(
    'pending'
  );
  const [mediaPermission, setMediaPermission] = useState<'granted' | 'denied' | 'pending'>(
    'pending'
  );
  const [isLoading, setIsLoading] = useState(false);

  // Check for existing permissions on component mount
  useEffect(() => {
    checkLocationPermission();
    checkMediaPermission();
  }, []);

  const checkLocationPermission = async () => {
    const { status } = await Location.getForegroundPermissionsAsync();
    setLocationPermission(status === 'granted' ? 'granted' : 'denied');
  };

  const checkMediaPermission = async () => {
    const { status } = await ImagePicker.getMediaLibraryPermissionsAsync();
    setMediaPermission(status === 'granted' ? 'granted' : 'denied');
  };

  const requestLocationPermission = async () => {
    const { status } = await Location.requestForegroundPermissionsAsync();
    setLocationPermission(status === 'granted' ? 'granted' : 'denied');

    if (status !== 'granted') {
      Alert.alert(
        'Location Permission Required',
        'Location permission is required to use this app. Please enable it in your device settings.',
        [{ text: 'OK' }]
      );
    }
  };

  const requestMediaPermission = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    setMediaPermission(status === 'granted' ? 'granted' : 'denied');

    if (status !== 'granted') {
      Alert.alert(
        'Media Permission Required',
        'Media library permission is required to use this app. Please enable it in your device settings.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleContinue = () => {
    if (locationPermission !== 'granted' || mediaPermission !== 'granted') {
      Alert.alert(
        'Permissions Required',
        'You need to grant all permissions to continue using the app.',
        [{ text: 'OK' }]
      );
      return;
    }

    setIsLoading(true);

    // Update user data
    updateUserData({ permissionsGranted: true });

    // Move to next step (completion)
    setTimeout(() => {
      setIsLoading(false);
      onNext();
    }, 500);
  };

  return (
    <ScrollView className="flex-1 px-6" showsVerticalScrollIndicator={false}>
      <View className="items-center mt-4 mb-6">
        <FontAwesome5 name="shield-alt" size={60} color={colors.primary} />
      </View>

      <Text className="mb-2 text-xl font-bold">App Permissions</Text>

      <Text className="mb-6 text-gray-500 font-base">
        To provide you with the full experience, we need the following permissions:
      </Text>

      <View className="p-4 mb-6 bg-yellow-100 rounded-lg">
        <Text className="text-sm text-yellow-800">
          <Text className="font-bold">Important:</Text> These permissions are required to use the
          app. Without them, you won't be able to continue.
        </Text>
      </View>

      <View className="mb-6 space-y-4">
        <View
          className={`rounded-xl border p-4 ${
            locationPermission === 'granted'
              ? 'border-green-500 bg-green-50'
              : 'border-gray-300 bg-gray-50'
          }`}>
          <View className="flex-row items-center mb-2">
            <FontAwesome5
              name="map-marker-alt"
              size={18}
              color={locationPermission === 'granted' ? '#10b981' : '#6b7280'}
            />
            <Text className="ml-2 font-medium">Location Permission</Text>
            {locationPermission === 'granted' && (
              <View className="ml-auto">
                <FontAwesome5 name="check-circle" size={18} color="#10b981" />
              </View>
            )}
          </View>
          <Text className="mb-3 text-sm text-gray-600">
            Required to show nearby events, find people around you, and provide accurate directions.
          </Text>
          {locationPermission !== 'granted' && (
            <Button onPress={requestLocationPermission} className="h-10 rounded-lg bg-violet-600">
              <ButtonText className="text-sm text-white">Allow Location Access</ButtonText>
            </Button>
          )}
        </View>

        <View
          className={`rounded-xl border p-4 ${
            mediaPermission === 'granted'
              ? 'border-green-500 bg-green-50'
              : 'border-gray-300 bg-gray-50'
          }`}>
          <View className="flex-row items-center mb-2">
            <FontAwesome5
              name="images"
              size={18}
              color={mediaPermission === 'granted' ? '#10b981' : '#6b7280'}
            />
            <Text className="ml-2 font-medium">Media Access</Text>
            {mediaPermission === 'granted' && (
              <View className="ml-auto">
                <FontAwesome5 name="check-circle" size={18} color="#10b981" />
              </View>
            )}
          </View>
          <Text className="mb-3 text-sm text-gray-600">
            Required to upload profile photos, share event pictures, and save event tickets.
          </Text>
          {mediaPermission !== 'granted' && (
            <Button onPress={requestMediaPermission} className="h-10 rounded-lg bg-violet-600">
              <ButtonText className="text-sm text-white">Allow Media Access</ButtonText>
            </Button>
          )}
        </View>
      </View>

      <Button
        className={`mb-8 mt-4 h-14 flex-row rounded-xl ${
          locationPermission !== 'granted' || mediaPermission !== 'granted'
            ? isDark
              ? 'bg-gray-700'
              : 'bg-gray-300'
            : isDark
              ? 'bg-violet-700'
              : 'bg-violet-600'
        }`}
        onPress={handleContinue}
        isDisabled={locationPermission !== 'granted' || mediaPermission !== 'granted' || isLoading}>
        <ButtonText className="font-bold text-white">
          {isLoading ? 'Processing...' : 'Continue'}
        </ButtonText>
      </Button>
    </ScrollView>
  );
}
