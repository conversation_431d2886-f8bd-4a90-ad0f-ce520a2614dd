import React, { useRef, forwardRef, useImperativeHandle, useState } from 'react';
import { View, Text, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import { useColorScheme } from '~/lib/useColorScheme';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import BottomSheet, { BottomSheetView, BottomSheetModal } from '@gorhom/bottom-sheet';
import { RenderBackdrop } from '../RenderBackdrop';
import * as ImagePicker from 'expo-image-picker';
import { FileService } from '~/services/FileService';
import { Toast } from 'toastify-react-native';

interface EventPhoto {
  id: string;
  secureUrl: string;
  publicId?: string;
}

export interface EventPhotoUploadBottomSheetProps {
  eventId: string;
  onPhotoUploaded: (photos: EventPhoto[]) => void;
}

export interface EventPhotoUploadBottomSheetHandle {
  present: () => void;
  dismiss: () => void;
}

const EventPhotoUploadBottomSheet = forwardRef<
  EventPhotoUploadBottomSheetHandle,
  EventPhotoUploadBottomSheetProps
>(({ eventId, onPhotoUploaded }, ref) => {
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const { colorScheme, colors } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [isUploading, setIsUploading] = useState(false);

  useImperativeHandle(ref, () => ({
    present: () => {
      bottomSheetModalRef.current?.expand();
    },
    dismiss: () => bottomSheetModalRef.current?.close(),
  }));

  const uploadImageFile = async (uri: string) => {
    try {
      setIsUploading(true);

      // Create file object from URI
      const filename = uri.split('/').pop() || 'image.jpg';
      const fileType = filename.split('.').pop() || 'jpg';

      const file = {
        uri,
        type: `image/${fileType}`,
        name: filename,
      } as any;

      const response = await FileService.uploadManyImages([file], eventId, 'EVENT_UPLOAD');

      // Update local photos state with the latest data from the response
      onPhotoUploaded(response.body.eventUploads || []);

      bottomSheetModalRef.current?.close();
      Toast.show({
        type: 'success',
        text1: 'Photo Added',
        text2: 'Your photo has been added to the event.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } catch (error: any) {
      console.error('Error uploading image:', error);
      Toast.show({
        type: 'error',
        text1: 'Upload Failed',
        text2: error.message || 'Failed to upload photo. Please try again.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } finally {
      setIsUploading(false);
    }
  };

  const takePhoto = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();

    if (status !== 'granted') {
      Alert.alert('Permission required', 'Camera permission is required to take photos');
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [16, 9],
      quality: 0.8,
    });

    if (!result.canceled && result.assets && result.assets.length > 0) {
      await uploadImageFile(result.assets[0].uri);
    }
  };

  const chooseFromLibrary = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (status !== 'granted') {
      Alert.alert('Permission required', 'Gallery permission is required to select photos');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [16, 9],
      quality: 0.8,
      allowsMultipleSelection: false,
    });

    if (!result.canceled && result.assets && result.assets.length > 0) {
      await uploadImageFile(result.assets[0].uri);
    }
  };

  return (
    <BottomSheet
      ref={bottomSheetModalRef}
      index={-1}
      snapPoints={['60%']}
      backdropComponent={RenderBackdrop}
      backgroundStyle={{
        backgroundColor: colors.background,
        borderTopLeftRadius: 24,
        borderTopRightRadius: 24,
      }}
      handleIndicatorStyle={{
        backgroundColor: colors.grey,
        width: 40,
        height: 4,
      }}>
      <BottomSheetView style={{ flex: 1, padding: 20 }}>
        <View className="mb-6">
          <Text className={`text-xl font-bold ${isDark ? 'text-white' : 'text-black'}`}>
            Add Event Photo
          </Text>
          <Text className={`mt-2 text-base ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
            Choose how you'd like to add a photo to your event
          </Text>
        </View>

        <View className="flex gap-3">
          <TouchableOpacity
            onPress={takePhoto}
            disabled={isUploading}
            className={`h-14 flex-row items-center rounded-lg px-4 py-3 ${isUploading ? 'opacity-50' : ''}`}
            style={{ backgroundColor: colors.grey5 }}>
            {isUploading ? (
              <ActivityIndicator
                size="small"
                color={isDark ? '#fff' : '#000'}
                style={{ marginRight: 12 }}
              />
            ) : (
              <Ionicons
                name="camera"
                size={22}
                color={isDark ? '#fff' : '#000'}
                style={{ marginRight: 12 }}
              />
            )}
            <Text className={`font-medium ${isDark ? 'text-white' : 'text-black'}`}>
              {isUploading ? 'Uploading...' : 'Take Photo'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={chooseFromLibrary}
            disabled={isUploading}
            className={`h-14 flex-row items-center rounded-lg px-4 py-3 ${isUploading ? 'opacity-50' : ''}`}
            style={{ backgroundColor: colors.grey5 }}>
            <MaterialCommunityIcons
              name="image"
              size={22}
              color={isDark ? '#fff' : '#000'}
              style={{ marginRight: 12 }}
            />
            <Text className={`font-medium ${isDark ? 'text-white' : 'text-black'}`}>
              Choose from Gallery
            </Text>
          </TouchableOpacity>
        </View>
      </BottomSheetView>
    </BottomSheet>
  );
});

EventPhotoUploadBottomSheet.displayName = 'EventPhotoUploadBottomSheet';

export default EventPhotoUploadBottomSheet;
