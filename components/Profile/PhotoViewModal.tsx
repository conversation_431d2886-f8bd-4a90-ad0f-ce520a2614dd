import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import {
  View,
  Modal,
  Image,
  TouchableOpacity,
  Dimensions,
  StatusBar,
} from 'react-native';

interface ProfilePhoto {
  id: string;
  secureUrl: string;
  publicId?: string;
}

interface PhotoViewModalProps {
  visible: boolean;
  photo: ProfilePhoto | null;
  onClose: () => void;
}

const { width, height } = Dimensions.get('window');

export default function PhotoViewModal({ visible, photo, onClose }: PhotoViewModalProps) {
  if (!photo) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
      statusBarTranslucent>
      <StatusBar backgroundColor="rgba(0,0,0,0.9)" barStyle="light-content" />
      <View className="flex-1 bg-black/90 items-center justify-center">
        <TouchableOpacity
          className="absolute top-12 right-4 z-10 rounded-full bg-black/50 p-3"
          onPress={onClose}>
          <Ionicons name="close" size={24} color="#fff" />
        </TouchableOpacity>
        
        <Image
          source={{ uri: photo.secureUrl }}
          style={{
            width: width,
            height: height,
          }}
          resizeMode="contain"
        />
      </View>
    </Modal>
  );
}
