import React, { useEffect, useLayoutEffect, useState } from 'react';
import {
  View,
  TouchableOpacity,
  Image,
  Text,
  StyleSheet,
  Keyboard,
  KeyboardAvoidingView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import {
  GiftedChat,
  Bubble,
  Send,
  InputToolbar,
  Actions,
  MessageImage,
  Day,
  Composer,
} from 'react-native-gifted-chat';
import { ChatItem, Message } from '../../types/chat_type';
import { moderateScale } from 'react-native-size-matters';
import { Platform } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useNavigation } from 'expo-router';

interface ActiveChatProps {
  selectedChat: ChatItem;
  messages: Message[];
  onSend: (messages: any) => void;
  onBack: () => void;
  renderActions?: any;
  colors: any;
  isDark: boolean;
  user: {
    _id: number;
    name: string;
    avatar: string;
  };
  renderSend?: any;
  renderInputToolbar?: any;
  renderComposer?: any;
}

interface RenderProps {
  text?: string;
  onSend?: (message: any, shouldReset: boolean) => void;
  textInputProps?: any;
  [key: string]: any;
}

const ActiveChat = ({
  selectedChat,
  messages,
  onSend,
  onBack,
  renderActions,
  colors,
  isDark,
  user,
  renderSend: customRenderSend,
  renderInputToolbar: customRenderInputToolbar,
  renderComposer: customRenderComposer,
}: ActiveChatProps) => {
  const [onFocus, setOnFocus] = useState(false);
  const insets = useSafeAreaInsets();

  const renderFooter = (props) => {
    return <View {...props} height={40}></View>;
  };

  const [isKeyboardVisible, setKeyboardVisible] = useState(false);
  const navigation = useNavigation();
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      setKeyboardVisible(true);
    });
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setKeyboardVisible(false);
    });

    return () => {
      keyboardDidHideListener.remove();
      keyboardDidShowListener.remove();
    };
  }, []);

  useLayoutEffect(() => {
    navigation.setOptions({
      tabBarStyle: { display: 'none' },
    });

    return () => {
      navigation.setOptions({
        tabBarStyle: { display: 'flex' },
      });
    };
  }, [navigation]);

  const renderBubble = (props: RenderProps) => {
    return (
      <Bubble
        {...props}
        wrapperStyle={{
          right: {
            backgroundColor: colors.primary,
            borderRadius: 18,
            paddingHorizontal: 2,
          },
          left: {
            backgroundColor: isDark ? colors.grey5 : colors.grey6,
            borderRadius: 18,
            paddingHorizontal: 2,
          },
        }}
        textStyle={{
          right: {
            color: 'white',
          },
          left: {
            color: colors.foreground,
          },
        }}
        bottomContainerStyle={{
          right: {
            marginBottom: 4,
          },
          left: {
            marginBottom: 4,
          },
        }}
      />
    );
  };

  const defaultRenderSend = (props: RenderProps) => {
    return (
      <Send
        {...props}
        containerStyle={{
          justifyContent: 'center',
          alignItems: 'center',
          alignSelf: 'center',
          justifySelf: 'flex-end',
          marginRight: 8,
          marginBottom: 0,
          height: 36,
          width: 36,
          borderRadius: 18,
          backgroundColor: colors.primary,
        }}>
        <Ionicons name="send" size={20} color="white" />
      </Send>
    );
  };

  const defaultRenderInputToolbar = (props: RenderProps) => {
    return (
      <InputToolbar
        {...props}
        containerStyle={{
          backgroundColor: isDark ? colors.grey6 : colors.root,
          borderTopColor: colors.grey5,
          borderTopWidth: 0.5,
          padding: 6,
        }}
        primaryStyle={{
          borderRadius: 20,
          backgroundColor: isDark ? colors.grey5 : colors.grey6,
          paddingHorizontal: 12,
          marginHorizontal: 8,
        }}
      />
    );
  };

  const renderComposer = (props: RenderProps) => {
    return (
      <View
        className="flex-row items-center pt-2 "
        style={{
          backgroundColor: colors.background,
          borderColor: colors.grey5,
        }}>
        {renderActions()}
        <Composer
          {...props}
          textInputProps={{
            onFocus: () => setOnFocus(true),
            onBlur: () => setOnFocus(false),
            marginHorizontal: 10,
            blurOnSubmit: true,
            paddingVertical: 0,
            paddingHorizontal: 15,
            backgroundColor: colors.grey5,
            borderRadius: 16,
            borderColor: colors.grey5,
            borderWidth: 1,
            width: '80%',
            minHeight: 44,
            marginTop: 3,
            marginBottom: 3,
            shadowColor: '#4a4de7',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 2,
            color: colors.foreground,
          }}></Composer>
        <Send {...props}>
          <View
            style={{
              justifyContent: 'center',
              height: '100%',
              marginRight: moderateScale(10),
              marginBottom: moderateScale(5),
              backgroundColor: '#4a4de7',
              width: 36,
              height: 36,
              borderRadius: 18,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Ionicons name="send-sharp" size={18} color={'white'} />
          </View>
        </Send>
      </View>
    );
  };

  const renderMessageImage = (props: RenderProps) => {
    return (
      <MessageImage
        {...props}
        imageStyle={{
          borderRadius: 15,
          marginVertical: 5,
        }}
      />
    );
  };

  const renderDay = (props: RenderProps) => {
    return (
      <Day
        {...props}
        textStyle={{
          color: colors.grey,
          fontSize: 12,
        }}
        containerStyle={{
          marginVertical: 10,
        }}
      />
    );
  };

  const renderChatEmpty = () => {
    return (
      <View style={[styles.emptyChatContainer, { transform: [{ scaleY: -1 }] }]}>
        <View style={styles.emptyChatContent}>
          <Text style={[styles.emptyChatTitle, { color: colors.primary }]}>
            Start Chatting with {selectedChat.name}
          </Text>

          <View style={styles.suggestionCards}>
            <View
              style={[
                styles.card,
                { backgroundColor: isDark ? colors.grey5 : '#ffffff', borderColor: colors.grey5 },
              ]}>
              <Text style={[styles.cardTitle, { color: colors.foreground }]}>Say Hello</Text>
              <Text style={[styles.cardDescription, { color: isDark ? colors.grey : '#666666' }]}>
                Start a conversation by introducing yourself and greeting them.
              </Text>
            </View>

            <View
              style={[
                styles.card,
                { backgroundColor: isDark ? colors.grey5 : '#ffffff', borderColor: colors.grey5 },
              ]}>
              <Text style={[styles.cardTitle, { color: colors.foreground }]}>Ask Questions</Text>
              <Text style={[styles.cardDescription, { color: isDark ? colors.grey : '#666666' }]}>
                Get to know more about them by asking about their interests.
              </Text>
            </View>

            <View
              style={[
                styles.card,
                { backgroundColor: isDark ? colors.grey5 : '#ffffff', borderColor: colors.grey5 },
              ]}>
              <Text style={[styles.cardTitle, { color: colors.foreground }]}>
                Share Experiences
              </Text>
              <Text style={[styles.cardDescription, { color: isDark ? colors.grey : '#666666' }]}>
                Tell them about your day or share something interesting.
              </Text>
            </View>
          </View>
        </View>
      </View>
    );
  };

  return (
    <View
      className="h-full flex-1"
      style={{
        backgroundColor: colors.background,
        paddingBottom: isKeyboardVisible ? moderateScale(0) : moderateScale(0),
      }}>
      <View
        className="h-[50px] flex-row items-center border-b px-4"
        style={{
          backgroundColor: colors.background,
          borderBottomColor: colors.grey5,
        }}>
        <TouchableOpacity className="p-1.5" onPress={onBack} activeOpacity={0.7}>
          <Ionicons name="arrow-back" size={24} color={colors.primary} />
        </TouchableOpacity>
        <View className="ml-2.5 flex-1 flex-row items-center">
          <Image source={{ uri: selectedChat.avatar }} className="h-8 w-8 rounded-full" />
          <Text
            className="ml-2.5 flex-1 text-lg font-semibold"
            style={{ color: colors.foreground }}
            numberOfLines={1}>
            {selectedChat.name}
          </Text>
        </View>
      </View>

      <KeyboardAvoidingView
        className="flex-1"
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}>
        <GiftedChat
          messages={messages}
          textInputStyle={{ fontFamily: 'Regular' }}
          onSend={(messages) => onSend(messages)}
          user={user}
          minInputToolbarHeight={0}
          bottomOffset={0}
          renderBubble={renderBubble}
          renderInputToolbar={(props) => (
            <InputToolbar {...props} containerStyle={{ borderTopWidth: 0 }} />
          )}
          renderSend={customRenderSend || defaultRenderSend}
          renderComposer={customRenderComposer || renderComposer}
          renderChatEmpty={renderChatEmpty}
          renderMessageImage={renderMessageImage}
          renderDay={renderDay}
          keyboardShouldPersistTaps="never"
          maxComposerHeight={45}
          listViewProps={{
            showsVerticalScrollIndicator: false,
          }}
          alwaysShowSend
          scrollToBottom
          scrollToBottomComponent={() => (
            <View
              className="h-8 w-8 items-center justify-center rounded-full"
              style={{
                backgroundColor: colors.primary,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.3,
                shadowRadius: 3,
                elevation: 3,
              }}>
              <Ionicons name="chevron-down" size={16} color="white" />
            </View>
          )}
          timeTextStyle={{
            left: { color: colors.grey, fontSize: 10 },
            right: { color: 'rgba(255, 255, 255, 0.7)', fontSize: 10 },
          }}
          textInputProps={{
            placeholder: 'Message...',
            placeholderTextColor: colors.grey,
            style: {
              color: colors.foreground,
              backgroundColor: isDark ? colors.grey5 : colors.grey6,
              borderRadius: 18,
              paddingHorizontal: 15,
              paddingVertical: 10,
              fontSize: 16,
            },
          }}
          showUserAvatar={true}
          showAvatarForEveryMessage={false}
          maxInputLength={1000}
        />
      </KeyboardAvoidingView>
    </View>
  );
};

const styles = StyleSheet.create({
  emptyChatContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    padding: 20,
  },
  emptyChatContent: {
    width: '100%',
    maxWidth: 400,
    padding: 10,
  },
  emptyChatTitle: {
    fontSize: 22,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 30,
  },
  suggestionCards: {
    gap: 16,
  },
  card: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 8,
  },
  cardDescription: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default ActiveChat;
