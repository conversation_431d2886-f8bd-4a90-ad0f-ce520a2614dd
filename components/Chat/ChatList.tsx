import React from 'react';
import { View, FlatList, TouchableOpacity, Image, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ChatItem } from '../../types/chat_type';
import { COLORS } from '../../theme/colors';

interface ChatListProps {
  data: ChatItem[];
  type: 'direct' | 'community' | 'event';
  onSelect: (item: ChatItem, type: 'direct' | 'community' | 'event') => void;
  isDark: boolean;
  colors: any;
  headerTitle: string;
  showAddButton?: boolean;
  onAddPress?: () => void;
  onBrowsePress?: () => void;
}

const ChatList = ({
  data,
  type,
  onSelect,
  isDark,
  colors,
  headerTitle,
  showAddButton = false,
  onAddPress,
  onBrowsePress,
}: ChatListProps) => {
  const renderItem = ({ item }: { item: ChatItem }) => {
    return (
      <TouchableOpacity
        className="flex-row items-center border-b py-3"
        style={{ borderBottomColor: colors.grey5 }}
        onPress={() => onSelect(item, type)}
        activeOpacity={0.7}>
        <Image
          source={{ uri: item.avatar }}
          className="w-15 h-15 rounded-full"
          style={{ width: 60, height: 60 }}
        />
        <View className="ml-4 flex-1 justify-center">
          <View className="mb-1 flex-row items-center justify-between">
            <Text
              className="mr-2 flex-1 font-medium text-lg"
              style={{ color: colors.foreground }}
              numberOfLines={1}
              ellipsizeMode="tail">
              {item.name}
            </Text>
            <Text className="text-xs" style={{ color: colors.grey }}>
              {item.timestamp}
            </Text>
          </View>
          <View className="flex-row items-center justify-between">
            <Text
              className="mr-2 flex-1 text-sm"
              style={{ color: colors.grey }}
              numberOfLines={1}
              ellipsizeMode="tail">
              {item.lastMessage || 'No messages yet'}
            </Text>
            {item.unread && item.unread > 0 && (
              <View
                className="h-5 w-5 items-center justify-center rounded-full"
                style={{ backgroundColor: colors.primary }}>
                <Text className="text-xs font-semibold text-white">{item.unread}</Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => {
    if (type === 'community') {
      return (
        <View className="flex-1 items-center justify-center">
          <Ionicons name="people-outline" size={80} color={colors.grey} />
          <Text className="mb-6 mt-4 text-center text-lg" style={{ color: colors.foreground }}>
            You are not in any communities
          </Text>
          <TouchableOpacity
            className="rounded-full px-4 py-3"
            style={{ backgroundColor: colors.primary }}
            onPress={onBrowsePress}>
            <Text className="font-medium text-white">Browse Communities</Text>
          </TouchableOpacity>
        </View>
      );
    }
    return null;
  };

  const isCommunity = type === 'community';

  return (
    <View className="flex-1 px-4">
      <View className="my-3 flex-row items-center justify-between">
        <Text className="font-bold text-xl" style={{ color: colors.foreground }}>
          {headerTitle}
        </Text>
        <View className="flex-row">
          {isCommunity && (
            <TouchableOpacity className="mr-2 p-1" onPress={onBrowsePress}>
              <Ionicons name="search" size={24} color={colors.primary} />
            </TouchableOpacity>
          )}
          {(showAddButton || isCommunity) && (
            <TouchableOpacity className="p-1" onPress={onAddPress}>
              <Ionicons
                name={isCommunity ? 'add' : 'add-circle-outline'}
                size={24}
                color={colors.primary}
              />
            </TouchableOpacity>
          )}
        </View>
      </View>
      <FlatList
        data={data}
        keyExtractor={(item) => item.id.toString()}
        renderItem={renderItem}
        className="flex-1"
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={renderEmptyState}
      />
    </View>
  );
};

export default ChatList;
