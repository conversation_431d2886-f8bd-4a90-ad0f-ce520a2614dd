import React, { useState } from 'react';
import { View, Text, FlatList, TouchableOpacity, Image, TextInput } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';

// Mock data for contacts/friends
const mockFriends = [
  {
    id: 1,
    name: '<PERSON>',
    avatar: 'https://placeimg.com/140/140/people',
    status: 'online',
  },
  {
    id: 2,
    name: '<PERSON>',
    avatar: 'https://placeimg.com/140/140/people',
    status: 'offline',
  },
  {
    id: 3,
    name: '<PERSON>',
    avatar: 'https://placeimg.com/140/140/people',
    status: 'online',
  },
  {
    id: 4,
    name: '<PERSON>',
    avatar: 'https://placeimg.com/140/140/people',
    status: 'offline',
  },
  {
    id: 5,
    name: '<PERSON>',
    avatar: 'https://placeimg.com/140/140/people',
    status: 'online',
  },
  {
    id: 6,
    name: '<PERSON>',
    avatar: 'https://placeimg.com/140/140/people',
    status: 'online',
  },
];

interface Friend {
  id: number;
  name: string;
  avatar: string;
  status: string;
}

interface NewChatSheetProps {
  bottomSheetRef: React.RefObject<BottomSheet>;
  onSelectFriend: (friend: Friend) => void;
  colors: any;
  isDark: boolean;
}

const NewChatSheet = ({ bottomSheetRef, onSelectFriend, colors, isDark }: NewChatSheetProps) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [friends, setFriends] = useState(mockFriends);

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    if (text.trim() === '') {
      setFriends(mockFriends);
    } else {
      const filtered = mockFriends.filter((friend) =>
        friend.name.toLowerCase().includes(text.toLowerCase())
      );
      setFriends(filtered);
    }
  };

  const renderFriend = ({ item }: { item: Friend }) => (
    <TouchableOpacity
      className="flex-row items-center px-4 py-3 border-b"
      style={{ borderBottomColor: colors.grey5 }}
      onPress={() => onSelectFriend(item)}>
      <View className="relative">
        <Image source={{ uri: item.avatar }} className="w-12 h-12 rounded-full" />
        <View
          className="absolute bottom-0 right-0 w-3 h-3 border-2 rounded-full"
          style={{
            backgroundColor: item.status === 'online' ? '#22c55e' : '#94a3b8',
            borderColor: isDark ? colors.background : colors.root,
          }}
        />
      </View>
      <View className="ml-3">
        <Text className="text-base font-medium" style={{ color: colors.foreground }}>
          {item.name}
        </Text>
        <Text className="text-sm" style={{ color: colors.grey }}>
          {item.status === 'online' ? 'Online' : 'Offline'}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <BottomSheetView style={{ backgroundColor: colors.background, flex: 1 }}>
      <View className="px-4 pt-2 pb-6">
        <Text className="mb-4 text-xl font-bold text-center" style={{ color: colors.foreground }}>
          New Chat
        </Text>

        <View className="mb-4">
          <View
            className="flex-row items-center rounded-xl px-3 py-2.5"
            style={{ backgroundColor: colors.grey5 }}>
            <Ionicons name="search" size={20} color={colors.grey} />
            <TextInput
              className="flex-1 ml-2 text-base"
              placeholder="Search friends..."
              placeholderTextColor={colors.grey}
              value={searchQuery}
              onChangeText={handleSearch}
              style={{ color: colors.foreground }}
            />
            {searchQuery ? (
              <TouchableOpacity onPress={() => handleSearch('')}>
                <Ionicons name="close-circle" size={20} color={colors.grey} />
              </TouchableOpacity>
            ) : null}
          </View>
        </View>

        <Text className="mb-2 text-base font-semibold" style={{ color: colors.grey }}>
          Friends
        </Text>

        {friends.length > 0 ? (
          <FlatList
            data={friends}
            keyExtractor={(item) => item.id.toString()}
            renderItem={renderFriend}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 40 }}
          />
        ) : (
          <View className="items-center justify-center flex-1 py-10">
            <Ionicons name="people" size={48} color={colors.grey} />
            <Text className="mt-4 text-base text-center" style={{ color: colors.grey }}>
              No friends found with this name
            </Text>
          </View>
        )}
      </View>
    </BottomSheetView>
  );
};

export default NewChatSheet;
