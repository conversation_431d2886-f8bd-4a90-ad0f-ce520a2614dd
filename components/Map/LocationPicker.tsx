import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  TouchableWithoutFeedback,
  StyleSheet,
} from 'react-native';
import Mapbox, { Camera, MapView, MarkerView, UserLocation } from '@rnmapbox/maps';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { Text } from '~/components/nativewindui/Text';
import { Button } from '@/components/ui/button';
import * as Location from 'expo-location';
import { useColorScheme } from '~/lib/useColorScheme';

// Set access token explicitly
Mapbox.setAccessToken(
  'pk.eyJ1IjoiZW1hY2xpYW0iLCJhIjoiY2wxNHRpcGNiMGR1dzNla2FndWVpdmJxbyJ9.wnxGUxO6rO3CoGaKyuXbVA'
);

export type LocationData = {
  coordinates: [number, number]; // [longitude, latitude]
  name: string;
  address?: string;
  manualAddress?: string; // Optional manual address entered by the user
};

type LocationPickerProps = {
  onSelectLocation: (location: LocationData) => void;
  onClose: () => void;
  initialLocation?: LocationData;
  userLocation?: [number, number]; // Optional user location coordinates
};

const LocationPicker: React.FC<LocationPickerProps> = ({
  onSelectLocation,
  onClose,
  initialLocation,
  userLocation,
}) => {
  const { colorScheme, colors } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [keyboardVisible, setKeyboardVisible] = useState(false);

  const mapRef = useRef<MapView>(null);
  const cameraRef = useRef<Camera>(null);
  const manualAddressInputRef = useRef<TextInput>(null);
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(
    initialLocation || null
  );
  const [manualAddress, setManualAddress] = useState('');
  const [loadingCurrentLocation, setLoadingCurrentLocation] = useState(false);

  // Set up keyboard listeners
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      setKeyboardVisible(true);
    });
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setKeyboardVisible(false);
    });

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  // Calculate map height based on keyboard visibility
  const mapHeight = keyboardVisible ? '25%' : '60%';

  const mapStyle = isDark
    ? 'mapbox://styles/mapbox/dark-v11'
    : 'mapbox://styles/mapbox/outdoors-v12';

  // Use user location on mount if provided
  useEffect(() => {
    if (!initialLocation && userLocation) {
      // If no initial location but user location provided, use that
      setSelectedLocation({
        coordinates: userLocation,
        name: 'Event Location',
      });

      // Fly to user location
      cameraRef.current?.setCamera({
        centerCoordinate: userLocation,
        zoomLevel: 14,
        animationDuration: 500,
      });
    } else if (initialLocation) {
      // Otherwise if initial location provided, use that
      setSelectedLocation(initialLocation);
      setManualAddress(initialLocation.manualAddress || '');

      // Fly to initial location
      cameraRef.current?.setCamera({
        centerCoordinate: initialLocation.coordinates,
        zoomLevel: 14,
        animationDuration: 500,
      });
    } else {
      // If no locations provided, try to get current location
      getCurrentLocation();
    }
  }, [initialLocation, userLocation]);

  const handleMapPress = (event: any) => {
    const { coordinates } = event.geometry;
    setSelectedLocation({
      coordinates: coordinates as [number, number],
      name: 'Event Location',
    });
  };

  const getCurrentLocation = async () => {
    setLoadingCurrentLocation(true);
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission denied', 'Location permission is required to use this feature');
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      const { latitude, longitude } = location.coords;
      const coordinates: [number, number] = [longitude, latitude];

      setSelectedLocation({
        coordinates,
        name: 'Event Location',
      });

      // Fly to the user's location
      cameraRef.current?.setCamera({
        centerCoordinate: coordinates,
        zoomLevel: 14,
        animationDuration: 500,
      });
    } catch (error) {
      console.error('Error getting current location:', error);
      Alert.alert('Error', 'Could not get your current location');
    } finally {
      setLoadingCurrentLocation(false);
    }
  };

  const confirmLocation = () => {
    if (selectedLocation) {
      if (!manualAddress.trim()) {
        Alert.alert('Address Required', 'Please enter an address before confirming the location.');
        return;
      }
      onSelectLocation({
        ...selectedLocation,
        manualAddress: manualAddress.trim(),
      });
    }
  };

  // Helper to dismiss the keyboard
  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  return (
    <TouchableWithoutFeedback onPress={dismissKeyboard}>
      <View className="flex-1">
        <View style={{ height: Platform.OS === 'ios' ? mapHeight : mapHeight }}>
          <MapView
            ref={mapRef}
            style={{ height: '100%', width: '100%' }}
            styleURL={mapStyle}
            onPress={handleMapPress}
            scaleBarEnabled={false}
            logoEnabled={false}
            attributionEnabled={false}
            scrollEnabled // this should be true
            zoomEnabled
            pitchEnabled
            rotateEnabled>
            <Camera
              ref={cameraRef}
              zoomLevel={14}
              followUserLocation
              followZoomLevel={14}
              centerCoordinate={selectedLocation?.coordinates || userLocation}
            />

            {selectedLocation && (
              <MarkerView coordinate={selectedLocation.coordinates}>
                <View className="items-center">
                  <MaterialIcons name="location-on" size={50} color="#8b5cf6" />
                </View>
              </MarkerView>
            )}
          </MapView>

          {/* Current Location Button */}
          <View className="absolute right-4 top-4">
            <TouchableOpacity
              onPress={getCurrentLocation}
              className={`rounded-full p-3 shadow ${isDark ? 'bg-gray-800' : 'bg-white'}`}>
              {loadingCurrentLocation ? (
                <ActivityIndicator size="small" color={isDark ? '#d1d5db' : '#6b7280'} />
              ) : (
                <Ionicons name="locate" size={22} color={isDark ? '#d1d5db' : '#6b7280'} />
              )}
            </TouchableOpacity>
          </View>
        </View>

        {/* Location details and confirmation section */}
        <View className={`p-4 `} style={{ flex: 1, backgroundColor: colors.background }}>
          {selectedLocation && (
            <>
              <TextInput
                ref={manualAddressInputRef}
                value={manualAddress}
                onChangeText={setManualAddress}
                placeholder="Enter the full address"
                multiline={true}
                numberOfLines={2}
                textAlignVertical="top"
                returnKeyType="done"
                blurOnSubmit
                onSubmitEditing={dismissKeyboard}
                className={`h-24 rounded-xl p-3 font-medium text-base  placeholder:text-[${colors.grey}] ${isDark ? 'text-white' : 'text-black'}`}
                placeholderTextColor={isDark ? colors.grey : colors.grey}
                style={{ backgroundColor: colors.grey5 }}
              />
            </>
          )}

          <Button
            size="lg"
            variant="solid"
            isDisabled={!selectedLocation || !manualAddress.trim()}
            className={`mt-4 h-14 rounded-xl ${isDark ? 'bg-violet-700' : 'bg-violet-600'}`}
            onPress={() => {
              dismissKeyboard();
              confirmLocation();
            }}>
            <Text className="font-bold text-white">
              {selectedLocation ? 'Confirm Location' : 'Select a Location First'}
            </Text>
          </Button>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default LocationPicker;
