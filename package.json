{"name": "social_events", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start --dev-client", "ios": "DARK_MODE=media expo run:ios", "android": "DARK_MODE=media expo run:android", "build:dev": "eas build --profile development", "build:preview": "eas build --profile preview", "build:prod": "eas build --profile production", "prebuild": "expo prebuild", "lint": "eslint \"**/*.{js,jsx,ts,tsx}\" && prettier -c \"**/*.{js,jsx,ts,tsx,json}\"", "format": "eslint \"**/*.{js,jsx,ts,tsx}\" --fix && prettier \"**/*.{js,jsx,ts,tsx,json}\" --write", "web": "DARK_MODE=media expo start --web"}, "dependencies": {"@expo/html-elements": "^0.4.2", "@expo/react-native-action-sheet": "^4.1.1", "@expo/vector-icons": "^14.0.0", "@gluestack-ui/accordion": "^1.0.14", "@gluestack-ui/actionsheet": "^0.2.52", "@gluestack-ui/button": "^1.0.14", "@gluestack-ui/checkbox": "^0.1.39", "@gluestack-ui/form-control": "^0.1.19", "@gluestack-ui/icon": "^0.1.26", "@gluestack-ui/input": "^0.1.38", "@gluestack-ui/menu": "^0.2.43", "@gluestack-ui/modal": "^0.1.41", "@gluestack-ui/nativewind-utils": "^1.0.26", "@gluestack-ui/overlay": "^0.1.22", "@gluestack-ui/radio": "^0.1.40", "@gluestack-ui/select": "^0.1.31", "@gluestack-ui/textarea": "^0.1.24", "@gluestack-ui/toast": "^1.0.9", "@gorhom/bottom-sheet": "^5.1.2", "@hookform/resolvers": "^5.0.1", "@legendapp/motion": "^2.4.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.3.0", "@react-native-community/slider": "4.5.6", "@react-native-picker/picker": "2.11.0", "@react-navigation/bottom-tabs": "^7.0.5", "@react-navigation/drawer": "^7.0.0", "@react-navigation/native": "^7.0.3", "@rn-primitives/avatar": "^1.1.0", "@rn-primitives/slot": "^1.1.0", "@rnmapbox/maps": "^10.1.39", "@roninoss/icons": "^0.0.4", "@shopify/flash-list": "1.7.6", "ajv": "^8.17.1", "ajv-keywords": "^5.1.0", "axios": "^1.9.0", "babel-plugin-module-resolver": "^5.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^4.1.0", "expo": "^53.0.9", "expo-av": "~15.1.4", "expo-blur": "~14.1.4", "expo-build-properties": "~0.14.6", "expo-constants": "~17.1.6", "expo-dev-client": "~5.1.8", "expo-font": "~13.3.1", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.5", "expo-location": "~18.1.5", "expo-navigation-bar": "~4.2.4", "expo-router": "~5.0.7", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-store-review": "~8.1.5", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.55.0", "react-native": "0.79.2", "react-native-chart-kit": "^6.12.0", "react-native-confirmation-code-field": "^7.4.0", "react-native-copilot": "^3.3.3", "react-native-css-interop": "^0.1.22", "react-native-date-picker": "^5.0.11", "react-native-edge-to-edge": "^1.6.0", "react-native-gesture-handler": "~2.24.0", "react-native-gifted-chat": "^2.8.1", "react-native-keyboard-controller": "^1.17.0", "react-native-pager-view": "6.7.1", "react-native-reanimated": "~3.17.4", "react-native-reanimated-carousel": "^4.0.2", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-size-matters": "^0.4.2", "react-native-svg": "15.11.2", "react-native-uitextview": "^1.1.4", "react-native-web": "^0.20.0", "tailwind-merge": "^2.2.1", "toastify-react-native": "^7.2.0", "zod": "^3.24.2", "zustand": "^5.0.4"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "eslint": "^8.57.0", "eslint-config-universe": "^12.0.1", "jscodeshift": "^0.15.2", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.17", "typescript": "~5.8.3"}, "eslintConfig": {"extends": "universe/native", "root": true}, "private": true, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}