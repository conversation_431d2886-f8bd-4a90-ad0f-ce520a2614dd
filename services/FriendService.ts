import * as SecureStore from 'expo-secure-store';
import axiosInstance from './config';

export class FriendService {
  static async sendFriendRequest(requesterId: string, requesteeId: string) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.post(
        '/friend/v1/send-friend-request',
        {
          requesterId,
          requesteeId,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      return response.data;
    } catch (error) {
      throw error;
    }
  }
}
