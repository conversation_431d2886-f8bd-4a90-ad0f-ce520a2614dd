import * as SecureStore from 'expo-secure-store';
import axiosInstance from './config';

export class FileService {
  static async uploadImage(imageFile: File, email: string) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const formData = new FormData();
      formData.append('imageFile', imageFile);
      formData.append('email', email);

      const response = await axiosInstance.post('/file/v1/upload-image', formData, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const data = response.data;
      return data;
    } catch (error) {
      throw error;
    }
  }

  static async uploadManyImages(imageFiles: File[], id: string, type: string) {
    try {
      console.log(imageFiles);
      console.log(id, type);
      const token = await SecureStore.getItemAsync('accessToken');
      const formData = new FormData();
      imageFiles.forEach((file) => formData.append('imageFiles', file));
      formData.append('uploadType', type);
      formData.append('entityId', id);

      const response = await axiosInstance.post('/file/v1/upload-images', formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async createStory(imageFiles: File[], id: string) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const formData = new FormData();
      imageFiles.forEach((file) => formData.append('storyImages', file));
      formData.append('userId', id);

      const response = await axiosInstance.post('/story/v1/create-story', formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }
  static async deleteStory(queryParams: any = {}) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.delete('/story/v1/remove-story', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        params: queryParams,
      });

      if (response.status !== 200) {
        throw new Error('Failed to fetch events');
      }

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  static async getStories(queryParams: any = {}) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.get('/story/v1/get-my-stories', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        params: queryParams,
      });

      if (response.status !== 200) {
        throw new Error('Failed to fetch events');
      }

      return response.data;
    } catch (error) {
      throw error;
    }
  }
}
